#!/usr/bin/env python3
"""
Test script to reproduce the questionnaire submission issue
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
CLIENT_EMAIL = "<EMAIL>"
CLIENT_PASSWORD = "testpass123"

def login_client():
    """Login as client and get access token"""
    login_data = {
        "username": CLIENT_EMAIL,
        "password": CLIENT_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/clients/login", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login successful, token: {token[:20]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def get_questionnaire(token):
    """Get the available questionnaire"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/questionnaires/me/questionnaire", headers=headers)
    if response.status_code == 200:
        questionnaire = response.json()
        print(f"✅ Got questionnaire: {questionnaire['id']}")
        print(f"   Survey type ID: {questionnaire['survey_type_id']}")
        print(f"   Status: {questionnaire['status']}")
        print(f"   Questions and answers: {questionnaire.get('questions_and_ans', {})}")
        return questionnaire
    else:
        print(f"❌ Failed to get questionnaire: {response.status_code} - {response.text}")
        return None

def get_questions(token, survey_type_id):
    """Get questions for the survey type"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/questionnaires/public/survey/{survey_type_id}/questions", headers=headers)
    if response.status_code == 200:
        questions_data = response.json()
        questions = questions_data["data"]
        print(f"✅ Got {len(questions)} questions")
        return questions
    else:
        print(f"❌ Failed to get questions: {response.status_code} - {response.text}")
        return None

def submit_answers(token, questions):
    """Submit answers for all questions"""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # Create answers for all questions (using 'A' for all)
    answers = [
        {
            "question_id": q["question_id"],
            "selected_answer": "A"
        }
        for q in questions
    ]
    
    submit_data = {"answers": answers}
    
    print(f"📤 Submitting {len(answers)} answers...")
    print(f"   Sample answer: {answers[0] if answers else 'None'}")
    
    response = requests.put(f"{BASE_URL}/questionnaires/me/questionnaire/answers", 
                           headers=headers, json=submit_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Answers submitted successfully")
        print(f"   Questions and answers after submit: {result.get('questions_and_ans', {})}")
        return True
    else:
        print(f"❌ Failed to submit answers: {response.status_code} - {response.text}")
        return False

def finish_questionnaire(token):
    """Finish the questionnaire"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.post(f"{BASE_URL}/questionnaires/me/questionnaire/finish", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Questionnaire finished successfully")
        print(f"   Status: {result['status']}")
        return True
    else:
        print(f"❌ Failed to finish questionnaire: {response.status_code} - {response.text}")
        return False

def main():
    print("🧪 Testing questionnaire submission...")
    
    # Step 1: Login
    token = login_client()
    if not token:
        return
    
    # Step 2: Get questionnaire
    questionnaire = get_questionnaire(token)
    if not questionnaire:
        return
    
    # Step 3: Get questions
    questions = get_questions(token, questionnaire["survey_type_id"])
    if not questions:
        return
    
    # Step 4: Submit answers
    if not submit_answers(token, questions):
        return
    
    # Step 5: Finish questionnaire
    finish_questionnaire(token)

if __name__ == "__main__":
    main()
