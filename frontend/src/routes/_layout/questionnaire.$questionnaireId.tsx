import {
  Box,
  Container,
  Flex,
  <PERSON>ing,
  Text,
  Badge,
  Button,
  VStack,
  HStack,
  Stack,
  IconButton,
  Grid,
  GridItem,
  Accordion,
  Separator,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useParams, useNavigate } from "@tanstack/react-router"
import { FiArrowLeft, FiPrinter } from "react-icons/fi"
import { format } from "date-fns"
import { QuestionnairesService } from "@/client"
import QuestionnaireStatusBadge from "@/components/Questionnaires/QuestionnaireStatusBadge"
import { PieChart, Pie, Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer, Tooltip } from 'recharts';
export const Route = createFileRoute("/_layout/questionnaire/$questionnaireId")({
  component: QuestionnaireDetailsPage,
})

// A4 Size Box Component with Scroll
// A4 dimensions: 210mm × 297mm ≈ 794px × 1123px at 96 DPI
const A4Box = ({ children, ...props }: { children: React.ReactNode } & any) => (
  <Box
    width="794px"
    height="1123px"
    maxWidth="100%"
    mx="auto"
    bg="white"
    border="1px solid #e0e0e0"
    borderRadius="8px"
    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
    p={6}
    position="relative"
    overflow="auto"
    {...props}
  >
    {children}
  </Box>
)




// Reusable Report Info Row Component
interface ReportInfoRowProps {
  label: string
  value: string | number | null | undefined
  valueWeight?: "normal" | "semibold"
}

const ReportInfoRow = ({ label, value, valueWeight = "normal" }: ReportInfoRowProps) => (
  <Flex justify="space-between" align="center" py={2} borderBottom="1px solid" borderColor="gray.100">
    <Text fontSize="md" fontWeight="medium" color="gray.700" minW="200px">
      {label}
    </Text>
    <Text fontSize="md" color="gray.900" textAlign="right" fontWeight={valueWeight}>
      {value?.toString() || "N/A"}
    </Text>
  </Flex>
)

// Helper function to get survey type display name
const getSurveyTypeDisplayName = (surveyTypeName: string | null) => {
  if (!surveyTypeName) return "Unknown Assessment Type"

  switch (surveyTypeName) {
    case "3-6 years questionnaire":
      return "3-6 Years"
    case "7-11 years questionnaire":
      return "7-11 Years"
    case "12-15 years questionnaire":
      return "12-15 Years"
    default:
      return surveyTypeName
  }
}

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "MMM dd, yyyy 'at' HH:mm")
  } catch {
    return "Unknown date"
  }
}

// Helper function to get answer option labels
const getAnswerLabel = (answer: string) => {
  const labels: Record<string, string> = {
    A: "Performs significantly better than described (or shows exceptional talent)",
    B: "Completely matches the description",
    C: "Mostly matches the description",
    D: "Partially matches the description",
    E: "Rarely matches the description",
    F: "Does not match the description at all",
  }
  return labels[answer] || `Option ${answer}`
}

// Scoring calculation functions
const calculateAnswerScore = (selectedAnswer: string, totalQuestionsInCompetency: number): number => {
  // Maximum score per question = 15 / total_questions_in_competency
  const maxScorePerQuestion = 15.0 / totalQuestionsInCompetency

  // Answer scoring scale: A=6/6, B=5/6, C=4/6, D=3/6, E=2/6, F=1/6 of max score
  const answerWeights: Record<string, number> = {
    'A': 6,   // 6/6 of max score
    'B': 5,   // 5/6 of max score
    'C': 4,   // 4/6 of max score
    'D': 3,   // 3/6 of max score
    'E': 2,   // 2/6 of max score
    'F': 1    // 1/6 of max score
  }

  const weight = answerWeights[selectedAnswer.toUpperCase()] || 1
  return maxScorePerQuestion * (weight / 6)
}

const calculateCoreCompetencyScores = (answers: any[]): Record<string, number> => {
  // Group answers by core competency
  const competencyAnswers: Record<string, any[]> = {}

  for (const answer of answers) {
    const competencyName = answer.core_competency_name || ''
    if (competencyName) {
      if (!competencyAnswers[competencyName]) {
        competencyAnswers[competencyName] = []
      }
      competencyAnswers[competencyName].push(answer)
    }
  }

  // Calculate scores for each core competency
  const competencyScores: Record<string, number> = {}

  for (const [competencyName, competencyAnswerList] of Object.entries(competencyAnswers)) {
    const totalQuestions = competencyAnswerList.length
    let totalScore = 0.0

    for (const answer of competencyAnswerList) {
      const selectedAnswer = answer.selected_answer || 'F'
      const score = calculateAnswerScore(selectedAnswer, totalQuestions)
      totalScore += score
    }

    competencyScores[competencyName] = Math.round(totalScore * 10) / 10
  }

  return competencyScores
}

const calculateIntelligenceCategoryScores = (
  answers: any[],
  competencyScores: Record<string, number>
): Record<string, { totalScore: number; competencies: Record<string, number> }> => {
  // Group competencies by intelligence category
  const categoryCompetencies: Record<string, Set<string>> = {}

  for (const answer of answers) {
    const categoryName = answer.intelligence_category_name || ''
    const competencyName = answer.core_competency_name || ''
    if (categoryName && competencyName) {
      if (!categoryCompetencies[categoryName]) {
        categoryCompetencies[categoryName] = new Set()
      }
      categoryCompetencies[categoryName].add(competencyName)
    }
  }

  // Calculate category scores
  const categoryScores: Record<string, { totalScore: number; competencies: Record<string, number> }> = {}

  for (const [categoryName, competencies] of Object.entries(categoryCompetencies)) {
    let categoryTotal = 0.0
    const categoryCompetencyScores: Record<string, number> = {}

    for (const competencyName of competencies) {
      const competencyScore = competencyScores[competencyName] || 0.0
      categoryCompetencyScores[competencyName] = competencyScore
      categoryTotal += competencyScore
    }

    categoryScores[categoryName] = {
      totalScore: Math.round(categoryTotal * 10) / 10,
      competencies: categoryCompetencyScores
    }
  }

  return categoryScores
}

function QuestionnaireDetailsPage() {
  const navigate = useNavigate()
  const { questionnaireId } = useParams({ strict: false })

  // Get questionnaire details
  const { data: questionnaire, isLoading, error } = useQuery({
    queryKey: ["questionnaire-details", questionnaireId],
    queryFn: () => QuestionnairesService.questionnairesGetQuestionnaire({
      questionnaireId: questionnaireId || ""
    }),
    enabled: !!questionnaireId,
  })

  const handleBack = () => {
    // Navigate back to the client details page if we have client_id
    if (questionnaire?.client_id) {
      navigate({ to: "/client/$clientId", params: { clientId: questionnaire.client_id } })
    } else {
      navigate({ to: "/clients" })
    }
  }

  if (isLoading) {
    return <QuestionnaireDetailsSkeleton />
  }

  if (error || !questionnaire) {
    return (
      <Container maxW="full">
        <VStack gap={6} py={8}>
          <Text fontSize="lg" color="red.500">
            {error ? "Error loading questionnaire details" : "Questionnaire not found"}
          </Text>
          <Button onClick={handleBack}>
            <FiArrowLeft />
            Back
          </Button>
        </VStack>
      </Container>
    )
  }

  // Extract answers from questionnaire data
  const answers = (questionnaire.questions_and_ans?.answers || []) as any[]

  // Group answers by intelligence category
  const answersByCategory = answers.reduce((acc: Record<string, any[]>, answer: any) => {
    const category = answer.intelligence_category_name || "Other"
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(answer)
    return acc
  }, {} as Record<string, any[]>)

  // Calculate scores
  const competencyScores = calculateCoreCompetencyScores(answers)
  const categoryScores = calculateIntelligenceCategoryScores(answers, competencyScores)

  // Calculate total score
  const totalScore = Object.values(categoryScores).reduce((sum, category) => sum + category.totalScore, 0)

  // Define colors for intelligence categories (consistent with fake data)
  const categoryColors = [
    "#FF5733", "#F1C40F", "#6200EA", "#2ECC71",
    "#8E44AD", "#3498DB", "#F39C12", "#900C3F"
  ]

  // Transform category scores for PieChart
  const pieChartData = Object.entries(categoryScores)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([categoryName, categoryData], index) => ({
      name: categoryName,
      value: categoryData.totalScore,
      fill: categoryColors[index % categoryColors.length]
    }))

  // Transform competency scores for RadarChart
  // Create a consistent order for radar chart to prevent dot/line mismatch
  const allCompetencyNames = Object.keys(competencyScores).sort((a, b) => a.localeCompare(b))

  const radarChartData = allCompetencyNames.map((competencyName, index) => {
    const score = competencyScores[competencyName] || 0
    return {
      subject: competencyName, // Use 'subject' as it's more standard for radar charts
      value: Math.max(0, Number(score)), // Ensure positive numbers
      fullName: competencyName, // Keep full name for tooltip
      index: index // Add index for debugging
    }
  })

  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <IconButton
              aria-label="Back"
              onClick={handleBack}
              variant="outline"
              size="sm"
            >
              <FiArrowLeft />
            </IconButton>
            <Heading size="lg">Questionnaire Details</Heading>
          </HStack>
          <Button
            colorScheme="blue"
            size="sm"
            onClick={() => window.print()}
          >
            <FiPrinter />
            Print Details
          </Button>
        </Flex>

        {/* Basic Information Card */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Heading size="md" color="gray.700">
              Basic Information
            </Heading>

            <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
              <GridItem>
                <Stack gap={4}>
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Child Name:
                    </Text>
                    <Text fontSize="lg" fontWeight="semibold">
                      {questionnaire.name}
                    </Text>
                  </Flex>

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Assessment Type:
                    </Text>
                    <Text fontSize="lg">
                      {getSurveyTypeDisplayName(questionnaire.survey_type_name)} Assessment
                    </Text>
                  </Flex>

                  {questionnaire.birth_info && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Birth Info:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.birth_info}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.grade_level && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Grade Level:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.grade_level}
                      </Text>
                    </Flex>
                  )}
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={4}>
                  {questionnaire.caregiver && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Caregiver:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.caregiver}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.feeding && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Feeding Method:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.feeding}
                      </Text>
                    </Flex>
                  )}

                  {questionnaire.native_language && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Native Language:
                      </Text>
                      <Text fontSize="lg">
                        {questionnaire.native_language}
                      </Text>
                    </Flex>
                  )}

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Status:
                    </Text>
                    <QuestionnaireStatusBadge status={questionnaire.status} />
                  </Flex>
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={4}>
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Created:
                    </Text>
                    <Text fontSize="lg">
                      {formatDate(questionnaire.created_at)}
                    </Text>
                  </Flex>

                  {questionnaire.finished_at && (
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="medium" color="gray.600">
                        Finished:
                      </Text>
                      <Text fontSize="lg">
                        {formatDate(questionnaire.finished_at)}
                      </Text>
                    </Flex>
                  )}

                  <Flex justify="space-between" align="center">
                    <Text fontWeight="medium" color="gray.600">
                      Total Questions:
                    </Text>
                    <Text fontSize="lg" fontWeight="semibold">
                      {answers.length} questions
                    </Text>
                  </Flex>
                </Stack>
              </GridItem>
            </Grid>
          </VStack>
        </Box>

        {/* A4 Report Section */}
        <A4Box>
          <VStack gap={6} align="stretch">
            {/* Report cover */}
            <VStack gap={8} align="center" justify="center" minH="400px" textAlign="center">
              {/* Logo */}
              <Box>
                <img
                  src="/assets/images/logo.webp"
                  alt="Company Logo"
                  style={{
                    width: "120px",
                    height: "auto",
                    objectFit: "contain"
                  }}
                />
              </Box>

              {/* Report Title */}
              <VStack gap={3}>
                <Heading size="2xl" color="gray.800" fontFamily="Inter" fontWeight="bold">
                  智能核心評估報告
                </Heading>
                <Text fontSize="lg" color="gray.600" fontWeight="medium">
                  Intelligence Core Assessment Report
                </Text>
              </VStack>

              {/* Child Information */}
              <VStack gap={2} mt={6}>
                <Text fontSize="xl" color="gray.700" fontWeight="semibold">
                  學生姓名 Student Name
                </Text>
                <Text fontSize="2xl" color="gray.900" fontWeight="bold"
                  border="2px solid" borderColor="gray.300"
                  px={6} py={3} borderRadius="md" minW="300px">
                  {questionnaire.name || "N/A"}
                </Text>
              </VStack>

              {/* Assessment Type */}
              <VStack gap={2}>
                <Text fontSize="lg" color="gray.700" fontWeight="medium">
                  評估類型 Assessment Type
                </Text>
                <Text fontSize="xl" color="blue.600" fontWeight="semibold">
                  {getSurveyTypeDisplayName(questionnaire.survey_type_name)} 評估
                </Text>
              </VStack>

              {/* Report Date */}
              <VStack gap={2} mt={8}>
                <Text fontSize="md" color="gray.600">
                  報告生成日期 Report Generated Date
                </Text>
                <Text fontSize="lg" color="gray.800" fontWeight="medium">
                  {formatDate(new Date().toISOString())}
                </Text>
              </VStack>
            </VStack>

            {/* Page Break for Print */}
            <Box
              pageBreakAfter="always"
              style={{ pageBreakAfter: "always" }}
              h="1px"
              w="100%"
              bg="transparent"
            />

            {/* Report child info */}
            <Box textAlign="center" borderBottom="2px solid" borderColor="gray.300" pb={4} mt={8}>
              <Heading size="lg" color="gray.800" fontFamily="Inter">
                學生訊息
              </Heading>

            </Box>

            {/* Report Content - Straight List Format */}
            <VStack gap={4} align="stretch" pt={4}>
              <ReportInfoRow
                label="Survey Type ID:"
                value={questionnaire.survey_type_id}
              />

              <ReportInfoRow
                label="Survey Type:"
                value={getSurveyTypeDisplayName(questionnaire.survey_type_name)}
              />

              <ReportInfoRow
                label="Child Name:"
                value={questionnaire.name}
                valueWeight="semibold"
              />

              <ReportInfoRow
                label="Birth Information:"
                value={questionnaire.birth_info}
              />

              <ReportInfoRow
                label="Caregiver:"
                value={questionnaire.caregiver}
              />

              <ReportInfoRow
                label="Feeding Method:"
                value={questionnaire.feeding}
              />

              <ReportInfoRow
                label="Native Language:"
                value={questionnaire.native_language}
              />

              <ReportInfoRow
                label="Grade Level:"
                value={questionnaire.grade_level}
              />
            </VStack>

            {/* Page Break for Print */}
            <Box
              pageBreakAfter="always"
              style={{ pageBreakAfter: "always" }}
              h="1px"
              w="100%"
              bg="transparent"
            />

            {/* Scoring Report Section */}
            <VStack gap={6} align="stretch" mt={8}>
              {/* Report Header */}
              <Box textAlign="center" borderBottom="2px solid" borderColor="blue.500" pb={4}>
                <Heading size="xl" color="blue.600" fontFamily="Inter" mb={2}>
                  智能核心評估分數報告
                </Heading>
                <Text fontSize="lg" color="gray.600" fontFamily="Inter">
                  Intelligence Core Assessment Scoring Report
                </Text>
                <Text fontSize="md" color="gray.500" fontFamily="Inter" mt={2}>
                  Completed: {questionnaire.finished_at ? formatDate(questionnaire.finished_at) : "In Progress"}
                </Text>
              </Box>

              {/* Overall Score Summary */}
              <Box bg="gray.50" p={6} borderRadius="lg" border="1px solid" borderColor="gray.200">
                <Flex justify="space-between" align="center">
                  <Text fontSize="xl" fontWeight="bold" color="gray.800" fontFamily="Inter">
                    Overall Assessment Score
                  </Text>
                  <Box textAlign="right">
                    <Text fontSize="3xl" fontWeight="bold" color="blue.600" fontFamily="Inter">
                      {Math.round(totalScore * 10) / 10}
                    </Text>
                    <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                      Maximum: {Object.keys(categoryScores).length * 60} points
                    </Text>
                  </Box>
                </Flex>
              </Box>

              {/* Intelligence Assessment Charts */}
              <Box>
                <Text fontSize="xl" fontWeight="bold" color="gray.800" fontFamily="Inter" mb={4}>
                  Visual Assessment Overview
                </Text>

                {/* Debug info - can be removed later */}
                {process.env.NODE_ENV === 'development' && (
                  <Box mb={4} p={3} bg="gray.100" borderRadius="md" fontSize="sm">
                    <Text fontWeight="bold" mb={2}>Debug Info:</Text>
                    <Text>Total competencies: {Object.keys(competencyScores).length}</Text>
                    <Text>Radar chart data points: {radarChartData.length}</Text>
                    <Text>Sample radar data: {radarChartData.slice(0, 3).map(item => `${item.subject}: ${item.value}`).join(', ')}</Text>
                    <Text>Score range: {Math.min(...Object.values(competencyScores))} - {Math.max(...Object.values(competencyScores))}</Text>
                    <Text>Data order: {radarChartData.map(item => item.subject).join(', ')}</Text>
                  </Box>
                )}

                <Box position="relative" width="100%" height="600px" maxWidth="700px" mx="auto">
                  {/* Pie Chart for Intelligence Categories */}
                  <Box position="absolute" top="0" left="0" zIndex={1} width="100%" height="100%">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieChartData}
                          dataKey="value"
                          cx="50%"
                          cy="50%"
                          innerRadius={150}
                          outerRadius={180}
                          fill="#82ca9d"
                          label={({ name, value }) => `${name}: ${value}`}
                        />
                        <Tooltip
                          formatter={(value, name) => [`${value} points`, name]}
                          labelFormatter={() => 'Intelligence Category'}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>

                  {/* Radar Chart for Core Competencies */}
                  <Box position="absolute" top="0" left="0" zIndex={2} width="100%" height="100%">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart
                        key={`radar-${radarChartData.length}`}
                        cx="50%"
                        cy="50%"
                        outerRadius={110}
                        data={radarChartData}
                        startAngle={40}
                        endAngle={400}
                        margin={{ top: 30, right: 30, bottom: 30, left: 30 }}
                      >
                        <PolarGrid
                          gridType="polygon"
                          stroke="#e0e0e0"
                          strokeWidth={1}
                        />
                        <PolarAngleAxis
                          dataKey="name"
                          tick={{
                            fontSize: 9,
                            fill: "black",
                            fontWeight: "bold",
                          }}
                          className="recharts-polar-angle-axis-tick"
                        />
                        <PolarRadiusAxis
                          angle={90}
                          domain={[0, 15]}
                          tick={{
                            fontSize: 8,
                            fill: "#666",
                          }}
                          tickCount={4}
                          axisLine={false}
                        />
                        <Radar
                          dataKey="value"
                          stroke="#FF5733"
                          fill="#FF5733"
                          fillOpacity={0.4}
                          strokeWidth={3}
                          connectNulls={false}
                          dot={{
                            r: 4,
                            fill: "#FF5733",
                            stroke: "#FFFFFF",
                            strokeWidth: 1
                          }}
                          activeDot={{
                            r: 6,
                            fill: "#FF5733",
                            stroke: "#FFFFFF",
                            strokeWidth: 1
                          }}
                        />
                        <Tooltip
                          formatter={(value, _name) => [`${Number(value).toFixed(1)} points`, 'Score']}
                          labelFormatter={(label) => `Core Competency: ${label}`}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid #ccc',
                            borderRadius: '4px'
                          }}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </Box>
                </Box>
              </Box>

              {/* Detailed Scoring by Intelligence Categories */}
              <Box>
                <Text fontSize="xl" fontWeight="bold" color="gray.800" fontFamily="Inter" mb={4}>
                  Detailed Scoring Report
                </Text>

                <Grid templateColumns="repeat(2, 1fr)" gap={6}>
                  {Object.entries(categoryScores)
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([categoryName, categoryData]) => (
                      <GridItem key={categoryName}>
                        <Box border="1px solid" borderColor="gray.200" borderRadius="lg" p={4} bg="white">
                          {/* Intelligence Category Header */}
                          <Flex justify="space-between" align="center" mb={3} pb={2} borderBottom="1px solid" borderColor="gray.100">
                            <Text fontSize="lg" fontWeight="bold" color="blue.600" fontFamily="Inter">
                              {categoryName}
                            </Text>
                            <Text fontSize="xl" fontWeight="bold" color="blue.600" fontFamily="Inter">
                              {categoryData.totalScore}
                            </Text>
                          </Flex>

                          {/* Core Competencies */}
                          <VStack align="stretch" gap={2}>
                            {Object.entries(categoryData.competencies)
                              .sort(([a], [b]) => a.localeCompare(b))
                              .map(([competencyName, competencyScore]) => (
                                <Flex key={competencyName} justify="space-between" pl={4}>
                                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                                    - {competencyName}
                                  </Text>
                                  <Text fontSize="sm" fontWeight="semibold" color="gray.800" fontFamily="Inter">
                                    {competencyScore}
                                  </Text>
                                </Flex>
                              ))}
                          </VStack>
                        </Box>
                      </GridItem>
                    ))}
                </Grid>
              </Box>

              {/* Scoring Legend */}
              <Box bg="gray.50" p={4} borderRadius="lg" border="1px solid" borderColor="gray.200">
                <Text fontSize="lg" fontWeight="bold" color="gray.800" fontFamily="Inter" mb={3}>
                  Scoring Explanation
                </Text>
                <VStack align="stretch" gap={2}>
                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                    • Each Core Competency has a maximum score of 15 points
                  </Text>
                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                    • Intelligence Category score is the sum of all its Core Competency scores
                  </Text>
                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                    • Answer scoring: A=6/6, B=5/6, C=4/6, D=3/6, E=2/6, F=1/6 of max score per question
                  </Text>
                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                    • Total score is the sum of all Intelligence Category scores
                  </Text>
                </VStack>
              </Box>
            </VStack>

            {/* Report Footer */}
            <Box textAlign="center" pt={8} mt={8} borderTop="1px solid" borderColor="gray.200">
              <Text fontSize="sm" color="gray.500">
                Generated on {formatDate(new Date().toISOString())}
              </Text>
            </Box>
          </VStack>
        </A4Box>

        {/* Questions and Answers Section */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Heading size="md" color="gray.700">
              Questions & Answers
            </Heading>

            {Object.keys(answersByCategory).length === 0 ? (
              <Text color="gray.500" textAlign="center" py={8}>
                No answers recorded yet.
              </Text>
            ) : (
              <Accordion.Root multiple>
                {Object.entries(answersByCategory).map(([category, categoryAnswers]) => (
                  <Accordion.Item key={category} value={category}>
                    <Accordion.ItemTrigger
                      bg="gray.50"
                      _hover={{ bg: "gray.100" }}
                      borderRadius="md"
                      p={4}
                      mb={2}
                    >
                      <Box flex="1" textAlign="left">
                        <Text fontSize="lg" fontWeight="bold" color="gray.800">
                          {category} ({categoryAnswers.length} questions)
                        </Text>
                      </Box>
                      <Accordion.ItemIndicator />
                    </Accordion.ItemTrigger>
                    <Accordion.ItemContent>
                      <Accordion.ItemBody p={4}>
                        <VStack gap={6} align="stretch">
                          {categoryAnswers.map((answer: any, index: number) => (
                            <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md" bg="gray.50">
                              <VStack align="flex-start" gap={4}>
                                <Text fontSize="md" fontWeight="bold" color="gray.800">
                                  Question {answer.question_id}
                                </Text>

                                <Text fontSize="sm" color="gray.700" lineHeight="1.6">
                                  {answer.question_text}
                                </Text>

                                <Separator />

                                <HStack align="flex-start" gap={2}>
                                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                                    Core Competency:
                                  </Text>
                                  <Text fontSize="sm" color="gray.800">
                                    {answer.core_competency_name}
                                  </Text>
                                </HStack>

                                <HStack align="flex-start" gap={2}>
                                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                                    Selected Answer:
                                  </Text>
                                  <Badge colorScheme="blue" fontSize="xs">
                                    {answer.selected_answer}
                                  </Badge>
                                </HStack>

                                <Box pl={4} borderLeft="3px solid" borderColor="blue.400">
                                  <Text fontSize="sm" color="gray.700" lineHeight="1.5">
                                    {getAnswerLabel(answer.selected_answer)}
                                  </Text>
                                </Box>
                              </VStack>
                            </Box>
                          ))}
                        </VStack>
                      </Accordion.ItemBody>
                    </Accordion.ItemContent>
                  </Accordion.Item>
                ))}
              </Accordion.Root>
            )}
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}

function QuestionnaireDetailsSkeleton() {
  return (
    <Container maxW="full">
      <VStack gap={6} py={8} align="stretch">
        {/* Header Skeleton */}
        <Flex justify="space-between" align="center">
          <HStack gap={4}>
            <Box w={10} h={10} bg="gray.200" borderRadius="md" />
            <Box w={48} h={8} bg="gray.200" borderRadius="md" />
          </HStack>
          <Box w={32} h={10} bg="gray.200" borderRadius="md" />
        </Flex>

        {/* Content Skeleton */}
        <Box
          bg="white"
          p={6}
          borderRadius="lg"
          shadow="sm"
          border="1px"
          borderColor="gray.200"
        >
          <VStack gap={6} align="stretch">
            <Box w={48} h={6} bg="gray.200" borderRadius="md" />
            <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
              {Array.from({ length: 3 }).map((_, i) => (
                <GridItem key={i}>
                  <Stack gap={4}>
                    {Array.from({ length: 4 }).map((_, j) => (
                      <Flex key={j} justify="space-between" align="center">
                        <Box w={24} h={5} bg="gray.200" borderRadius="md" />
                        <Box w={32} h={5} bg="gray.200" borderRadius="md" />
                      </Flex>
                    ))}
                  </Stack>
                </GridItem>
              ))}
            </Grid>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
