import {
  Table,
  Text,
  Icon<PERSON><PERSON>on,
  <PERSON>u,
} from "@chakra-ui/react"
import { FiMoreVertical, FiTrash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-icons/fi"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"


import type { QuestionnaireWithDetails, QuestionnaireStatusEnum } from "@/client"
import { QuestionnairesService } from "@/client"
import QuestionnaireStatusBadge from "./QuestionnaireStatusBadge"

interface QuestionnairesTableProps {
  questionnaires: QuestionnaireWithDetails[]
  clientId: string
}

export default function QuestionnairesTable({ questionnaires, clientId }: QuestionnairesTableProps) {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const deleteQuestionnaireMutation = useMutation({
    mutationFn: (questionnaireId: string) =>
      QuestionnairesService.questionnairesDeleteQuestionnaire({ questionnaireId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client-questionnaires", clientId] })
    },
    onError: (error: any) => {
      console.error("Failed to delete questionnaire:", error)
    },
  })

  const updateStatusMutation = useMutation({
    mutationFn: ({ questionnaireId, status }: { questionnaireId: string; status: QuestionnaireStatusEnum }) =>
      QuestionnairesService.questionnairesUpdateQuestionnaireStatus({ questionnaireId, status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client-questionnaires", clientId] })
    },
    onError: (error: any) => {
      console.error("Failed to update questionnaire status:", error)
    },
  })

  const handleDelete = (questionnaire: QuestionnaireWithDetails) => {
    if (confirm(`Are you sure you want to delete the questionnaire for "${questionnaire.name}"? This action cannot be undone.`)) {
      deleteQuestionnaireMutation.mutate(questionnaire.id)
    }
  }

  const handleMarkFinished = (questionnaire: QuestionnaireWithDetails) => {
    updateStatusMutation.mutate({
      questionnaireId: questionnaire.id,
      status: "finished"
    })
  }

  const handleViewDetails = (questionnaire: QuestionnaireWithDetails) => {
    navigate({ to: "/questionnaire/$questionnaireId", params: { questionnaireId: questionnaire.id } })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (questionnaires.length === 0) {
    return (
      <Text color="gray.500" textAlign="center" py={8}>
        No questionnaires found for this client.
      </Text>
    )
  }

  return (
    <>
      <Table.Root variant="outline">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>Created Date</Table.ColumnHeader>
            <Table.ColumnHeader>Name</Table.ColumnHeader>
            <Table.ColumnHeader>Survey Type</Table.ColumnHeader>
            <Table.ColumnHeader>Status</Table.ColumnHeader>
            <Table.ColumnHeader>Finished Date</Table.ColumnHeader>
            <Table.ColumnHeader width="50px">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {questionnaires.map((questionnaire) => (
            <Table.Row key={questionnaire.id}>
              <Table.Cell>{formatDate(questionnaire.created_at)}</Table.Cell>
              <Table.Cell>
                <Text fontWeight="medium">{questionnaire.name}</Text>
              </Table.Cell>
              <Table.Cell>{questionnaire.survey_type_name || "Not selected"}</Table.Cell>
              <Table.Cell>
                <QuestionnaireStatusBadge status={questionnaire.status} />
              </Table.Cell>
              <Table.Cell>
                {questionnaire.finished_at ? formatDate(questionnaire.finished_at) : "-"}
              </Table.Cell>
              <Table.Cell>
                <Menu.Root>
                  <Menu.Trigger asChild>
                    <IconButton
                      variant="ghost"
                      size="sm"
                      aria-label="Questionnaire actions"
                    >
                      <FiMoreVertical />
                    </IconButton>
                  </Menu.Trigger>
                  <Menu.Content>
                    {questionnaire.status === "finished" && (
                      <Menu.Item
                        value="view-details"
                        onClick={() => handleViewDetails(questionnaire)}
                      >
                        <FiEye />
                        View Details
                      </Menu.Item>
                    )}
                    {questionnaire.status === "available" && (
                      <Menu.Item
                        value="finish"
                        onClick={() => handleMarkFinished(questionnaire)}
                        disabled={updateStatusMutation.isPending}
                      >
                        <FiCheck />
                        Mark as Finished
                      </Menu.Item>
                    )}
                    <Menu.Item
                      value="delete"
                      onClick={() => handleDelete(questionnaire)}
                      color="red.500"
                      disabled={deleteQuestionnaireMutation.isPending}
                    >
                      <FiTrash2 />
                      Delete
                    </Menu.Item>
                  </Menu.Content>
                </Menu.Root>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

    </>
  )
}
