import { Button, Flex, Text, Box, useDisclosure, Link } from "@chakra-ui/react";
import { Drawer, DrawerBody, DrawerOverlay, DrawerContent } from "@chakra-ui/modal";
import { Link as RouterLink } from "@tanstack/react-router";
import { RiWhatsappFill, RiArrowRightSLine } from "react-icons/ri";
import { FaFacebook } from "react-icons/fa";
import { useSettings } from "@/hooks/useSettings";
import { IoMenu } from "react-icons/io5";
import { colors } from '../colors'
import { ChineseText } from "./ui/fonts";
import { FaUserCircle } from "react-icons/fa";
import useAuth from "@/hooks/useAuth";
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {QuestionnairesService } from "@/client"

const baseMenuItems = [
  {
    name: "主頁",
    path: "/",
  },
  {
    name: "MA心理動機評估",
    path: "/ma",
  },
  {
    name: "課程及活動",
    path: "/activitys",
  },
  {
    name: "關於我們",
    path: "/aboutus",
  },
  {
    name: "聯絡我們",
    path: "/contactus",
  },
]

export const MenuBar = () => {
  const { open, onOpen, onClose } = useDisclosure();
  const { data } = useSettings();
  const { isAuthenticated: initialAuth } = useAuth();
  const [isAuthenticated, setIsAuthenticated] = useState(initialAuth);

  // Get available questionnaire for authenticated users
  const { data: questionnaire } = useQuery({
    queryKey: ["my-questionnaire"],
    queryFn: () => QuestionnairesService.getMyAvailableQuestionnaire(),
    enabled: isAuthenticated, // Only fetch if authenticated
  });

  // Dynamic menu items based on auth and questionnaire availability
  const menuBarItems = [
    ...baseMenuItems.slice(0, 1), // "主頁"
    ...(isAuthenticated && questionnaire ? [{
      name: "智能核心評估",
      path: "/questionnaire-start",
    }] : []),
    ...(isAuthenticated ? [{
      name: "問卷記錄",
      path: "/questionnaire-history",
    }] : []),
    ...baseMenuItems.slice(1), // Rest of the items
  ];

  // Listen for auth state changes
  useEffect(() => {
    const handleAuthChange = (event: CustomEvent) => {
      setIsAuthenticated(event.detail.isAuthenticated);
      console.log('MenuBar received auth change:', event.detail.isAuthenticated);
    };

    window.addEventListener('authStateChanged', handleAuthChange as EventListener);

    return () => {
      window.removeEventListener('authStateChanged', handleAuthChange as EventListener);
    };
  }, []);

  // Update local state when hook state changes
  useEffect(() => {
    setIsAuthenticated(initialAuth);
  }, [initialAuth]);

  console.log('MenuBar isAuthenticated:', isAuthenticated); // Debug log

  return (
    <Flex w="100vw" align="center" justify="center">
      <Flex
        as="nav"
        align="center"
        justify="space-between"
        borderBottomWidth="1px"
        borderBottomColor="#A8A8A8"
        w='90vw'
        h="80px"
      >
        <Flex align="center"
          w={{ md: "30vw", lg: "25vw" }}
        >
          <Text fontSize="2xl" fontWeight="bold" color="black">
            Decoding Happiness
          </Text>
        </Flex>

        <Flex align="center" justify="center" w="50vw" gap={{ lg: "4", xl: "7" }} display={{ base: "none", md: "none", lg: "flex" }}>
          {menuBarItems.map((item) => (
            <RouterLink to={item.path} key={item.name}>
              <ChineseText color="black" fontWeight="bold" fontSize="sm">
                {item.name}
              </ChineseText>
            </RouterLink>
          ))}
        </Flex>
        <Flex align="center" justify="flex-end" w="25vw" display={{ base: "none", md: "none", lg: "flex" }}>
          <Flex align="center" justify="flex-end" gap="2">
            <Link href={data?.whatsapp ?? '#'} target="_blank" rel="noopener noreferrer">
              <RiWhatsappFill color={colors.mainColor} size={50} />
            </Link>
            {/* <Link href={data?.facebook ?? '#'} target="_blank" rel="noopener noreferrer">
              <FaFacebook color={colors.mainColor} size={50} />
            </Link> */}
            {isAuthenticated ? (
              <RouterLink
                to="/profile"
                key="profile-link"
              >
                <FaUserCircle size={45} color={colors.mainColor} />
              </RouterLink>
            ) : (
              <RouterLink to="/signin">
                <Button
                  h="50"
                  bgColor={colors.mainColor}
                  w="100"
                  display="block"
                  rounded="full"
                  _hover={{
                    boxShadow: "none",
                    outline: "none",
                    border: "none",
                    transform: "none",
                    bgColor: colors.mainColor,
                  }}
                >
                  <ChineseText
                    fontWeight="bold"
                    w="80px"
                    color="white"
                    fontSize="sm"
                  >
                    {"登入"}
                  </ChineseText>
                </Button>
              </RouterLink>
            )}
        
          </Flex>
        </Flex>
        <Flex align="center" justify="flex-end" w="25vw" display={{ base: "flex", md: "flex", lg: "none" }} gap={2}>
          <Link href={data?.whatsapp ?? '#'} target="_blank" rel="noopener noreferrer">
            <RiWhatsappFill color={colors.mainColor} size={40} />
          </Link>
          <Link href={data?.facebook ?? '#'} target="_blank" rel="noopener noreferrer">
            <FaFacebook color={colors.mainColor} size={40} />
          </Link>
          <Box as="span"
            cursor="pointer"
            onClick={open ? onClose : onOpen}
            aria-label="Open menu"
          >
            {/* {open ? <IoMenu size={24} /> : <IoMenu size={24} />} */}
            <IoMenu size={50} color={colors.mainColor} />
          </Box>
        </Flex>
      </Flex>

      <Drawer placement="right" onClose={onClose} isOpen={open}
        isFullHeight={true}
        trapFocus={true}
        blockScrollOnMount={false}
        size="xs"
      >
        <DrawerOverlay backgroundColor="transparent" />
        <DrawerContent
          //width={{ base: "250px", sm: "250px", md: "350px" }}
          maxW="300px"
          right="0"
          position="absolute"
          backgroundColor={"#F9F5E9"}
        >

          <DrawerBody p={0} mt={0}>
            <Flex
              h="80px"
              align={"center"}
              onClick={open ? onClose : onOpen}
            >
              <RiArrowRightSLine color={colors.mainColor} size={50} />
            </Flex>
            <Flex direction="column" w="full">
              {menuBarItems.map((item) => (
                <Box key={item.name} w="full" >
                  <RouterLink to={item.path} onClick={onClose}>
                    <Flex justify="center" w="full">
                      <Box
                        w="70%"
                        py={6}
                        px={6}
                        textAlign="center"
                        borderBottomWidth={1}
                      >
                        <ChineseText color="black" fontWeight="bold" fontSize="md">
                          {item.name}
                        </ChineseText>
                      </Box>
                    </Flex>
                  </RouterLink>
                  {/* {index < menuBarItems.length - 1 && (
                    <Box borderBottomWidth="1px" borderBottomColor="black" />
                  )} */}
                </Box>
              ))}
              <Box mt={6} display="flex" justifyContent="center" py={4}>
                {isAuthenticated ? (
                  <RouterLink to="/profile" onClick={onClose}>
                    <FaUserCircle size={45} color={colors.mainColor} />
                  </RouterLink>
                ) : (
                  <RouterLink to="/signin">
                    <Button
                      h="50"
                      bgColor={colors.mainColor}
                      w="70%"
                      rounded="full"
                      _hover={{
                        bgColor: colors.mainColor,
                      }}
                    >
                      <ChineseText
                        fontWeight="bold"
                        color="white"
                        fontSize="sm"
                      >
                        {"登入"}
                      </ChineseText>
                    </Button>
                  </RouterLink>
                )}
              </Box>
            </Flex>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </Flex>
  );
};

export default MenuBar;
