/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as FontsImport } from './routes/fonts'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutIndexImport } from './routes/_layout/index'
import { Route as LayoutVerifyEmailImport } from './routes/_layout/verify-email'
import { Route as LayoutSignupImport } from './routes/_layout/signup'
import { Route as LayoutSigninImport } from './routes/_layout/signin'
import { Route as LayoutQuestionnaireStartImport } from './routes/_layout/questionnaire-start'
import { Route as LayoutQuestionnaireResultsImport } from './routes/_layout/questionnaire-results'
import { Route as LayoutQuestionnaireQuestionsImport } from './routes/_layout/questionnaire-questions'
import { Route as LayoutQuestionnaireHistoryImport } from './routes/_layout/questionnaire-history'
import { Route as LayoutProfileImport } from './routes/_layout/profile'
import { Route as LayoutMaImport } from './routes/_layout/ma'
import { Route as LayoutContactusImport } from './routes/_layout/contactus'
import { Route as LayoutActivitysImport } from './routes/_layout/activitys'
import { Route as LayoutAboutusImport } from './routes/_layout/aboutus'
import { Route as LayoutQuestionnaireDetailsQuestionnaireIdImport } from './routes/_layout/questionnaire-details.$questionnaireId'
import { Route as LayoutActivityIdImport } from './routes/_layout/activity/$id'

// Create/Update Routes

const FontsRoute = FontsImport.update({
  id: '/fonts',
  path: '/fonts',
  getParentRoute: () => rootRoute,
} as any)

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const LayoutIndexRoute = LayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutVerifyEmailRoute = LayoutVerifyEmailImport.update({
  id: '/verify-email',
  path: '/verify-email',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSignupRoute = LayoutSignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSigninRoute = LayoutSigninImport.update({
  id: '/signin',
  path: '/signin',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQuestionnaireStartRoute = LayoutQuestionnaireStartImport.update({
  id: '/questionnaire-start',
  path: '/questionnaire-start',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQuestionnaireResultsRoute = LayoutQuestionnaireResultsImport.update(
  {
    id: '/questionnaire-results',
    path: '/questionnaire-results',
    getParentRoute: () => LayoutRoute,
  } as any,
)

const LayoutQuestionnaireQuestionsRoute =
  LayoutQuestionnaireQuestionsImport.update({
    id: '/questionnaire-questions',
    path: '/questionnaire-questions',
    getParentRoute: () => LayoutRoute,
  } as any)

const LayoutQuestionnaireHistoryRoute = LayoutQuestionnaireHistoryImport.update(
  {
    id: '/questionnaire-history',
    path: '/questionnaire-history',
    getParentRoute: () => LayoutRoute,
  } as any,
)

const LayoutProfileRoute = LayoutProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaRoute = LayoutMaImport.update({
  id: '/ma',
  path: '/ma',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutContactusRoute = LayoutContactusImport.update({
  id: '/contactus',
  path: '/contactus',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutActivitysRoute = LayoutActivitysImport.update({
  id: '/activitys',
  path: '/activitys',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutAboutusRoute = LayoutAboutusImport.update({
  id: '/aboutus',
  path: '/aboutus',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQuestionnaireDetailsQuestionnaireIdRoute =
  LayoutQuestionnaireDetailsQuestionnaireIdImport.update({
    id: '/questionnaire-details/$questionnaireId',
    path: '/questionnaire-details/$questionnaireId',
    getParentRoute: () => LayoutRoute,
  } as any)

const LayoutActivityIdRoute = LayoutActivityIdImport.update({
  id: '/activity/$id',
  path: '/activity/$id',
  getParentRoute: () => LayoutRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_layout': {
      id: '/_layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/fonts': {
      id: '/fonts'
      path: '/fonts'
      fullPath: '/fonts'
      preLoaderRoute: typeof FontsImport
      parentRoute: typeof rootRoute
    }
    '/_layout/aboutus': {
      id: '/_layout/aboutus'
      path: '/aboutus'
      fullPath: '/aboutus'
      preLoaderRoute: typeof LayoutAboutusImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/activitys': {
      id: '/_layout/activitys'
      path: '/activitys'
      fullPath: '/activitys'
      preLoaderRoute: typeof LayoutActivitysImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/contactus': {
      id: '/_layout/contactus'
      path: '/contactus'
      fullPath: '/contactus'
      preLoaderRoute: typeof LayoutContactusImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/ma': {
      id: '/_layout/ma'
      path: '/ma'
      fullPath: '/ma'
      preLoaderRoute: typeof LayoutMaImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/profile': {
      id: '/_layout/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof LayoutProfileImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-history': {
      id: '/_layout/questionnaire-history'
      path: '/questionnaire-history'
      fullPath: '/questionnaire-history'
      preLoaderRoute: typeof LayoutQuestionnaireHistoryImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-questions': {
      id: '/_layout/questionnaire-questions'
      path: '/questionnaire-questions'
      fullPath: '/questionnaire-questions'
      preLoaderRoute: typeof LayoutQuestionnaireQuestionsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-results': {
      id: '/_layout/questionnaire-results'
      path: '/questionnaire-results'
      fullPath: '/questionnaire-results'
      preLoaderRoute: typeof LayoutQuestionnaireResultsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-start': {
      id: '/_layout/questionnaire-start'
      path: '/questionnaire-start'
      fullPath: '/questionnaire-start'
      preLoaderRoute: typeof LayoutQuestionnaireStartImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/signin': {
      id: '/_layout/signin'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof LayoutSigninImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/signup': {
      id: '/_layout/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof LayoutSignupImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/verify-email': {
      id: '/_layout/verify-email'
      path: '/verify-email'
      fullPath: '/verify-email'
      preLoaderRoute: typeof LayoutVerifyEmailImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/': {
      id: '/_layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof LayoutIndexImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/activity/$id': {
      id: '/_layout/activity/$id'
      path: '/activity/$id'
      fullPath: '/activity/$id'
      preLoaderRoute: typeof LayoutActivityIdImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/questionnaire-details/$questionnaireId': {
      id: '/_layout/questionnaire-details/$questionnaireId'
      path: '/questionnaire-details/$questionnaireId'
      fullPath: '/questionnaire-details/$questionnaireId'
      preLoaderRoute: typeof LayoutQuestionnaireDetailsQuestionnaireIdImport
      parentRoute: typeof LayoutImport
    }
  }
}

// Create and export the route tree

interface LayoutRouteChildren {
  LayoutAboutusRoute: typeof LayoutAboutusRoute
  LayoutActivitysRoute: typeof LayoutActivitysRoute
  LayoutContactusRoute: typeof LayoutContactusRoute
  LayoutMaRoute: typeof LayoutMaRoute
  LayoutProfileRoute: typeof LayoutProfileRoute
  LayoutQuestionnaireHistoryRoute: typeof LayoutQuestionnaireHistoryRoute
  LayoutQuestionnaireQuestionsRoute: typeof LayoutQuestionnaireQuestionsRoute
  LayoutQuestionnaireResultsRoute: typeof LayoutQuestionnaireResultsRoute
  LayoutQuestionnaireStartRoute: typeof LayoutQuestionnaireStartRoute
  LayoutSigninRoute: typeof LayoutSigninRoute
  LayoutSignupRoute: typeof LayoutSignupRoute
  LayoutVerifyEmailRoute: typeof LayoutVerifyEmailRoute
  LayoutIndexRoute: typeof LayoutIndexRoute
  LayoutActivityIdRoute: typeof LayoutActivityIdRoute
  LayoutQuestionnaireDetailsQuestionnaireIdRoute: typeof LayoutQuestionnaireDetailsQuestionnaireIdRoute
}

const LayoutRouteChildren: LayoutRouteChildren = {
  LayoutAboutusRoute: LayoutAboutusRoute,
  LayoutActivitysRoute: LayoutActivitysRoute,
  LayoutContactusRoute: LayoutContactusRoute,
  LayoutMaRoute: LayoutMaRoute,
  LayoutProfileRoute: LayoutProfileRoute,
  LayoutQuestionnaireHistoryRoute: LayoutQuestionnaireHistoryRoute,
  LayoutQuestionnaireQuestionsRoute: LayoutQuestionnaireQuestionsRoute,
  LayoutQuestionnaireResultsRoute: LayoutQuestionnaireResultsRoute,
  LayoutQuestionnaireStartRoute: LayoutQuestionnaireStartRoute,
  LayoutSigninRoute: LayoutSigninRoute,
  LayoutSignupRoute: LayoutSignupRoute,
  LayoutVerifyEmailRoute: LayoutVerifyEmailRoute,
  LayoutIndexRoute: LayoutIndexRoute,
  LayoutActivityIdRoute: LayoutActivityIdRoute,
  LayoutQuestionnaireDetailsQuestionnaireIdRoute:
    LayoutQuestionnaireDetailsQuestionnaireIdRoute,
}

const LayoutRouteWithChildren =
  LayoutRoute._addFileChildren(LayoutRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof LayoutRouteWithChildren
  '/fonts': typeof FontsRoute
  '/aboutus': typeof LayoutAboutusRoute
  '/activitys': typeof LayoutActivitysRoute
  '/contactus': typeof LayoutContactusRoute
  '/ma': typeof LayoutMaRoute
  '/profile': typeof LayoutProfileRoute
  '/questionnaire-history': typeof LayoutQuestionnaireHistoryRoute
  '/questionnaire-questions': typeof LayoutQuestionnaireQuestionsRoute
  '/questionnaire-results': typeof LayoutQuestionnaireResultsRoute
  '/questionnaire-start': typeof LayoutQuestionnaireStartRoute
  '/signin': typeof LayoutSigninRoute
  '/signup': typeof LayoutSignupRoute
  '/verify-email': typeof LayoutVerifyEmailRoute
  '/': typeof LayoutIndexRoute
  '/activity/$id': typeof LayoutActivityIdRoute
  '/questionnaire-details/$questionnaireId': typeof LayoutQuestionnaireDetailsQuestionnaireIdRoute
}

export interface FileRoutesByTo {
  '/fonts': typeof FontsRoute
  '/aboutus': typeof LayoutAboutusRoute
  '/activitys': typeof LayoutActivitysRoute
  '/contactus': typeof LayoutContactusRoute
  '/ma': typeof LayoutMaRoute
  '/profile': typeof LayoutProfileRoute
  '/questionnaire-history': typeof LayoutQuestionnaireHistoryRoute
  '/questionnaire-questions': typeof LayoutQuestionnaireQuestionsRoute
  '/questionnaire-results': typeof LayoutQuestionnaireResultsRoute
  '/questionnaire-start': typeof LayoutQuestionnaireStartRoute
  '/signin': typeof LayoutSigninRoute
  '/signup': typeof LayoutSignupRoute
  '/verify-email': typeof LayoutVerifyEmailRoute
  '/': typeof LayoutIndexRoute
  '/activity/$id': typeof LayoutActivityIdRoute
  '/questionnaire-details/$questionnaireId': typeof LayoutQuestionnaireDetailsQuestionnaireIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_layout': typeof LayoutRouteWithChildren
  '/fonts': typeof FontsRoute
  '/_layout/aboutus': typeof LayoutAboutusRoute
  '/_layout/activitys': typeof LayoutActivitysRoute
  '/_layout/contactus': typeof LayoutContactusRoute
  '/_layout/ma': typeof LayoutMaRoute
  '/_layout/profile': typeof LayoutProfileRoute
  '/_layout/questionnaire-history': typeof LayoutQuestionnaireHistoryRoute
  '/_layout/questionnaire-questions': typeof LayoutQuestionnaireQuestionsRoute
  '/_layout/questionnaire-results': typeof LayoutQuestionnaireResultsRoute
  '/_layout/questionnaire-start': typeof LayoutQuestionnaireStartRoute
  '/_layout/signin': typeof LayoutSigninRoute
  '/_layout/signup': typeof LayoutSignupRoute
  '/_layout/verify-email': typeof LayoutVerifyEmailRoute
  '/_layout/': typeof LayoutIndexRoute
  '/_layout/activity/$id': typeof LayoutActivityIdRoute
  '/_layout/questionnaire-details/$questionnaireId': typeof LayoutQuestionnaireDetailsQuestionnaireIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/fonts'
    | '/aboutus'
    | '/activitys'
    | '/contactus'
    | '/ma'
    | '/profile'
    | '/questionnaire-history'
    | '/questionnaire-questions'
    | '/questionnaire-results'
    | '/questionnaire-start'
    | '/signin'
    | '/signup'
    | '/verify-email'
    | '/'
    | '/activity/$id'
    | '/questionnaire-details/$questionnaireId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/fonts'
    | '/aboutus'
    | '/activitys'
    | '/contactus'
    | '/ma'
    | '/profile'
    | '/questionnaire-history'
    | '/questionnaire-questions'
    | '/questionnaire-results'
    | '/questionnaire-start'
    | '/signin'
    | '/signup'
    | '/verify-email'
    | '/'
    | '/activity/$id'
    | '/questionnaire-details/$questionnaireId'
  id:
    | '__root__'
    | '/_layout'
    | '/fonts'
    | '/_layout/aboutus'
    | '/_layout/activitys'
    | '/_layout/contactus'
    | '/_layout/ma'
    | '/_layout/profile'
    | '/_layout/questionnaire-history'
    | '/_layout/questionnaire-questions'
    | '/_layout/questionnaire-results'
    | '/_layout/questionnaire-start'
    | '/_layout/signin'
    | '/_layout/signup'
    | '/_layout/verify-email'
    | '/_layout/'
    | '/_layout/activity/$id'
    | '/_layout/questionnaire-details/$questionnaireId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  LayoutRoute: typeof LayoutRouteWithChildren
  FontsRoute: typeof FontsRoute
}

const rootRouteChildren: RootRouteChildren = {
  LayoutRoute: LayoutRouteWithChildren,
  FontsRoute: FontsRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_layout",
        "/fonts"
      ]
    },
    "/_layout": {
      "filePath": "_layout.tsx",
      "children": [
        "/_layout/aboutus",
        "/_layout/activitys",
        "/_layout/contactus",
        "/_layout/ma",
        "/_layout/profile",
        "/_layout/questionnaire-history",
        "/_layout/questionnaire-questions",
        "/_layout/questionnaire-results",
        "/_layout/questionnaire-start",
        "/_layout/signin",
        "/_layout/signup",
        "/_layout/verify-email",
        "/_layout/",
        "/_layout/activity/$id",
        "/_layout/questionnaire-details/$questionnaireId"
      ]
    },
    "/fonts": {
      "filePath": "fonts.tsx"
    },
    "/_layout/aboutus": {
      "filePath": "_layout/aboutus.tsx",
      "parent": "/_layout"
    },
    "/_layout/activitys": {
      "filePath": "_layout/activitys.tsx",
      "parent": "/_layout"
    },
    "/_layout/contactus": {
      "filePath": "_layout/contactus.tsx",
      "parent": "/_layout"
    },
    "/_layout/ma": {
      "filePath": "_layout/ma.tsx",
      "parent": "/_layout"
    },
    "/_layout/profile": {
      "filePath": "_layout/profile.tsx",
      "parent": "/_layout"
    },
    "/_layout/questionnaire-history": {
      "filePath": "_layout/questionnaire-history.tsx",
      "parent": "/_layout"
    },
    "/_layout/questionnaire-questions": {
      "filePath": "_layout/questionnaire-questions.tsx",
      "parent": "/_layout"
    },
    "/_layout/questionnaire-results": {
      "filePath": "_layout/questionnaire-results.tsx",
      "parent": "/_layout"
    },
    "/_layout/questionnaire-start": {
      "filePath": "_layout/questionnaire-start.tsx",
      "parent": "/_layout"
    },
    "/_layout/signin": {
      "filePath": "_layout/signin.tsx",
      "parent": "/_layout"
    },
    "/_layout/signup": {
      "filePath": "_layout/signup.tsx",
      "parent": "/_layout"
    },
    "/_layout/verify-email": {
      "filePath": "_layout/verify-email.tsx",
      "parent": "/_layout"
    },
    "/_layout/": {
      "filePath": "_layout/index.tsx",
      "parent": "/_layout"
    },
    "/_layout/activity/$id": {
      "filePath": "_layout/activity/$id.tsx",
      "parent": "/_layout"
    },
    "/_layout/questionnaire-details/$questionnaireId": {
      "filePath": "_layout/questionnaire-details.$questionnaireId.tsx",
      "parent": "/_layout"
    }
  }
}
ROUTE_MANIFEST_END */
