{"openapi": "3.1.0", "info": {"title": "decoding happiness", "version": "0.1.0"}, "paths": {"/api/v1/login/access-token": {"post": {"tags": ["login"], "summary": "Login Access Token", "description": "OAuth2 compatible token login, get an access token for future requests", "operationId": "login-login_access_token", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login-login_access_token"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/login/test-token": {"post": {"tags": ["login"], "summary": "Test Token", "description": "Test access token", "operationId": "login-test_token", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/password-recovery/{email}": {"post": {"tags": ["login"], "summary": "Recover Password", "description": "Password Recovery", "operationId": "login-recover_password", "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string", "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reset-password/": {"post": {"tags": ["login"], "summary": "Reset Password", "description": "Reset password", "operationId": "login-reset_password", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPassword"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/password-recovery-html-content/{email}": {"post": {"tags": ["login"], "summary": "Recover Password Html Content", "description": "HTML Content for Password Recovery", "operationId": "login-recover_password_html_content", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string", "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"text/html": {"schema": {"type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/": {"get": {"tags": ["users"], "summary": "Read Users", "description": "Retrieve users.", "operationId": "users-read_users", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["users"], "summary": "Create User", "description": "Create new user.", "operationId": "users-create_user", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/me": {"get": {"tags": ["users"], "summary": "Read User Me", "description": "Get current user.", "operationId": "users-read_user_me", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "delete": {"tags": ["users"], "summary": "Delete User Me", "description": "Delete own user.", "operationId": "users-delete_user_me", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["users"], "summary": "Update User Me", "description": "Update own user.", "operationId": "users-update_user_me", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateMe"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/me/password": {"patch": {"tags": ["users"], "summary": "Update Password Me", "description": "Update own password.", "operationId": "users-update_password_me", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePassword"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/signup": {"post": {"tags": ["users"], "summary": "Register User", "description": "Create new user without the need to be logged in.", "operationId": "users-register_user", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegister"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Read User By Id", "description": "Get a specific user by id.", "operationId": "users-read_user_by_id", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["users"], "summary": "Update User", "description": "Update a user.", "operationId": "users-update_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["users"], "summary": "Delete User", "description": "Delete a user.", "operationId": "users-delete_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/utils/test-email/": {"post": {"tags": ["utils"], "summary": "Test Email", "description": "Test emails.", "operationId": "utils-test_email", "parameters": [{"name": "email_to", "in": "query", "required": true, "schema": {"type": "string", "format": "email", "title": "Email To"}}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/utils/health-check/": {"get": {"tags": ["utils"], "summary": "Health Check", "operationId": "utils-health_check", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "boolean", "title": "Response Utils-Health Check"}}}}}}}, "/api/v1/items/": {"get": {"tags": ["items"], "summary": "Read Items", "description": "Retrieve items.", "operationId": "items-read_items", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["items"], "summary": "Create <PERSON><PERSON>", "description": "Create new item.", "operationId": "items-create_item", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/items/{id}": {"get": {"tags": ["items"], "summary": "Read Item", "description": "Get item by ID.", "operationId": "items-read_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["items"], "summary": "Update Item", "description": "Update an item.", "operationId": "items-update_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["items"], "summary": "Delete Item", "description": "Delete an item.", "operationId": "items-delete_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/login": {"post": {"tags": ["clients"], "summary": "Client Login Access Token", "description": "OAuth2 compatible token login, get an access token for future requests", "operationId": "clients-client_login_access_token", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_clients-client_login_access_token"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/signup": {"post": {"tags": ["clients"], "summary": "Register Client", "description": "Create new client with email verification.", "operationId": "clients-register_client", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientRegister"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/verify-email/": {"post": {"tags": ["clients"], "summary": "<PERSON><PERSON><PERSON>", "description": "Verify client email", "operationId": "clients-verify_email", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string", "title": "Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/me": {"get": {"tags": ["clients"], "summary": "Read Client Me", "description": "Get current client profile.", "operationId": "clients-read_client_me", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["clients"], "summary": "Update Client Me", "description": "Update current client profile.", "operationId": "clients-update_client_me", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdateMe"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/admin_clients": {"get": {"tags": ["admin"], "summary": "Read Clients", "description": "Retrieve clients with pagination, sorting, and filtering.", "operationId": "admin-read_clients", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "Field to sort by", "default": "created_at", "title": "Sort By"}, "description": "Field to sort by"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "Sort order: asc or desc", "default": "desc", "title": "Sort Order"}, "description": "Sort order: asc or desc"}, {"name": "status_filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status: active, inactive, or all", "title": "Status Filter"}, "description": "Filter by status: active, inactive, or all"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["admin"], "summary": "Create Client Admin", "description": "Create new client (admin only).", "operationId": "admin-create_client_admin", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/{client_id}": {"get": {"tags": ["admin"], "summary": "Read Client Admin", "description": "Get a specific client by id (admin only).", "operationId": "admin-read_client_admin", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin_clients/{client_id}": {"patch": {"tags": ["admin"], "summary": "Update Client Admin", "description": "Update a client (admin only).", "operationId": "admin-update_client_admin", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["admin"], "summary": "Delete Client Admin", "description": "Delete a client (admin only).", "operationId": "admin-delete_client_admin", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/introduction/docs": {"post": {"tags": ["introduction"], "summary": "Create Introduction Docs", "description": "API Schema for documentation", "operationId": "introduction-create_introduction_docs", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntroductionCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Introduction"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/introduction/": {"post": {"tags": ["introduction"], "summary": "Create Introduction", "description": "Create new about us.", "operationId": "introduction-create_introduction", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_introduction-create_introduction"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Introduction"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["introduction"], "summary": "Read Introduction List", "operationId": "introduction-read_introduction_list", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntroductionsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/introduction/{id}": {"put": {"tags": ["introduction"], "summary": "Edit Introduction", "operationId": "introduction-edit_introduction", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_introduction-edit_introduction"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Introduction"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["introduction"], "summary": "Delete Introduction", "description": "Delete an course.", "operationId": "introduction-delete_introduction", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ma_course/": {"post": {"tags": ["ma_course"], "summary": "Create Macourse", "operationId": "ma_course-create_macourse", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_ma_course-create_macourse"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaCourseImage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["ma_course"], "summary": "Get Macourses", "operationId": "ma_course-get_macourses", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaCourseImagesPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ma_course/{id}": {"put": {"tags": ["ma_course"], "summary": "Edit <PERSON>", "description": "Edit an existing MaCourse.", "operationId": "ma_course-edit_macourse", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_ma_course-edit_macourse"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaCourseImage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["ma_course"], "summary": "Delete Macourse", "description": "Delete a MaCourse.", "operationId": "ma_course-delete_macourse", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Ma Course-Delete Macourse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blogs/": {"post": {"tags": ["blogs"], "summary": "Create Blog", "operationId": "blogs-create_blog", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_blogs-create_blog"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Blog"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["blogs"], "summary": "Get Blogs", "operationId": "blogs-get_blogs", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlogsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blogs/{id}": {"put": {"tags": ["blogs"], "summary": "Edit Blog", "operationId": "blogs-edit_blog", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_blogs-edit_blog"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Blog"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["blogs"], "summary": "Get Blog", "description": "Get a specific blog by its ID.", "operationId": "blogs-get_blog", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Blog"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["blogs"], "summary": "Delete Blog", "description": "Delete a blog.", "operationId": "blogs-delete_blog", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blogs/{id}/images": {"put": {"tags": ["blogs"], "summary": "Delete Blog Images", "operationId": "blogs-delete_blog_images", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_blogs-delete_blog_images"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Blog"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/aboutus/": {"post": {"tags": ["aboutus"], "summary": "Create About Us", "description": "Create new about us.", "operationId": "aboutus-create_about_us", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_aboutus-create_about_us"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AboutUs"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["aboutus"], "summary": "Read About Us List", "operationId": "aboutus-read_about_us_list", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AboutUsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/aboutus/{id}": {"put": {"tags": ["aboutus"], "summary": "Edit About Us", "operationId": "aboutus-edit_about_us", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_aboutus-edit_about_us"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AboutUs"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["aboutus"], "summary": "Delete About Us", "operationId": "aboutus-delete_about_us", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/setting/": {"get": {"tags": ["setting"], "summary": "Read Setting", "description": "Retrieve users.", "operationId": "setting-read_setting", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Setting"}}}}}}, "put": {"tags": ["setting"], "summary": "Update Setting", "description": "Update an item.", "operationId": "setting-update_setting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingBase"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/survey-types": {"get": {"tags": ["questionnaires"], "summary": "Get Survey Types", "description": "Get all survey types.", "operationId": "questionnaires-get_survey_types", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SurveyTypePublic"}, "type": "array", "title": "Response Questionnaires-Get Survey Types"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/survey/{survey_type_id}": {"get": {"tags": ["questionnaires"], "summary": "Get Survey Questions", "description": "Get all questions for a specific survey type with details.", "operationId": "questionnaires-get_survey_questions", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "survey_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Survey Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyQuestionsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/survey/{survey_type_id}/import-json": {"post": {"tags": ["questionnaires"], "summary": "Import Survey Questions Json", "description": "Import questions from JSON data. Deletes all existing questions for the survey type.\nExpected format: {\"questions\": [{\"intelligence_category\": \"...\", \"core_competency\": \"...\", \"question\": \"...\"}]}\n\nValidation Rules:\n- Must have exactly 8 different Intelligence Categories\n- Each Intelligence Category must have exactly 4 different Core Competencies\n\nReturns 400 error with detailed message if validation fails.", "operationId": "questionnaires-import_survey_questions_json", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "survey_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Survey Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionsImport"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/survey/{survey_type_id}/import": {"post": {"tags": ["questionnaires"], "summary": "Import Survey Questions", "description": "Import questions from CSV file. Deletes all existing questions for the survey type.\nExpected CSV headers: Intelligence Category, Core Competency, Question\n\nValidation Rules:\n- Must have exactly 8 different Intelligence Categories\n- Each Intelligence Category must have exactly 4 different Core Competencies\n\nReturns 400 error with detailed message if validation fails.", "operationId": "questionnaires-import_survey_questions", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "survey_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Survey Type Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_questionnaires-import_survey_questions"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/question/{question_id}": {"patch": {"tags": ["questionnaires"], "summary": "Update Question", "description": "Update a question. Only content and index can be updated.", "operationId": "questionnaires-update_question", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "question_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Question Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/clients/{client_id}/questionnaires": {"get": {"tags": ["questionnaires"], "summary": "Get Client Questionnaires", "description": "Get all questionnaires for a specific client (Admin only).", "operationId": "questionnaires-get_client_questionnaires", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairesPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["questionnaires"], "summary": "Create Client Questionnaire", "description": "Create a new questionnaire for a client (Admin only).\nOnly one 'available' questionnaire per client is allowed.", "operationId": "questionnaires-create_client_questionnaire", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/questionnaires/{questionnaire_id}/status": {"put": {"tags": ["questionnaires"], "summary": "Update Questionnaire Status", "description": "Update questionnaire status (Admin only).", "operationId": "questionnaires-update_questionnaire_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "questionnaire_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Questionnaire Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/QuestionnaireStatusEnum"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/questionnaires/{questionnaire_id}": {"get": {"tags": ["questionnaires"], "summary": "Get Questionnaire", "description": "Get detailed information about a specific questionnaire (Admin only).", "operationId": "questionnaires-get_questionnaire", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "questionnaire_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Questionnaire Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireWithDetails"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["questionnaires"], "summary": "Delete Questionnaire", "description": "Delete a questionnaire (Admin only).", "operationId": "questionnaires-delete_questionnaire", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "questionnaire_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Questionnaire Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/me/questionnaire": {"get": {"tags": ["questionnaires"], "summary": "Get My Available Questionnaire", "description": "Get the available questionnaire for the current client.\nReturns None if no available questionnaire exists.", "operationId": "questionnaires-get_my_available_questionnaire", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/QuestionnairePublic"}, {"type": "null"}], "title": "Response Questionnaires-Get My Available Questionnaire"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/me/questionnaires": {"get": {"tags": ["questionnaires"], "summary": "Get My Questionnaires", "description": "Get all questionnaires for the current client (both available and finished).", "operationId": "questionnaires-get_my_questionnaires", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairesPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/me/questionnaires/{questionnaire_id}": {"get": {"tags": ["questionnaires"], "summary": "Get My Questionnaire Details", "description": "Get detailed information about a specific questionnaire for the current client.", "operationId": "questionnaires-get_my_questionnaire_details", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "questionnaire_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Questionnaire Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireWithDetails"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/me/questionnaire/start": {"post": {"tags": ["questionnaires"], "summary": "Start My Questionnaire", "description": "Start filling out the questionnaire by saving basic info and survey type.\nUpdates the existing available questionnaire for the client.", "operationId": "questionnaires-start_my_questionnaire", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireStartRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/me/questionnaire/answers": {"put": {"tags": ["questionnaires"], "summary": "Submit Questionnaire Answers", "description": "Submit answers for the questionnaire.", "operationId": "questionnaires-submit_questionnaire_answers", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireSubmitRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/me/questionnaire/finish": {"post": {"tags": ["questionnaires"], "summary": "Finish My Questionnaire", "description": "Mark the questionnaire as finished.", "operationId": "questionnaires-finish_my_questionnaire", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnairePublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/questionnaires/public/survey-types": {"get": {"tags": ["questionnaires"], "summary": "Get Public Survey Types", "description": "Get all survey types (public endpoint for client-side).", "operationId": "questionnaires-get_public_survey_types", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SurveyTypePublic"}, "type": "array", "title": "Response Questionnaires-Get Public Survey Types"}}}}}}}, "/api/v1/questionnaires/public/survey/{survey_type_id}/questions": {"get": {"tags": ["questionnaires"], "summary": "Get Public Survey Questions", "description": "Get all questions for a specific survey type with details (public client-side endpoint).", "operationId": "questionnaires-get_public_survey_questions", "parameters": [{"name": "survey_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Survey Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyQuestionsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/questionnaires/me/questionnaire/{questionnaire_id}/scoring": {"get": {"tags": ["questionnaires"], "summary": "Get My Questionnaire Scoring", "description": "Get scoring results for a finished questionnaire.", "operationId": "questionnaires-get_my_questionnaire_scoring", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "questionnaire_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Questionnaire Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionnaireScoring"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/private/users/": {"post": {"tags": ["private"], "summary": "Create User", "description": "Create a new user.", "operationId": "private-create_user", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PrivateUserCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AboutUs": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Description"}, "index": {"type": "integer", "title": "Index"}, "image": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image"}, "status": {"type": "boolean", "title": "Status", "default": true}, "id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}}, "type": "object", "required": ["title", "index"], "title": "AboutUs"}, "AboutUsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/Introduction"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "AboutUsPublic"}, "Blog": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 11024}, {"type": "null"}], "title": "Description"}, "image1": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image1"}, "image2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image2"}, "image3": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image3"}, "status": {"type": "boolean", "title": "Status", "default": true}, "id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["title"], "title": "Blog"}, "BlogsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/Blog"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "BlogsPublic"}, "Body_aboutus-create_about_us": {"properties": {"description": {"type": "string", "title": "Description"}, "title": {"type": "string", "title": "Title"}, "image": {"type": "string", "format": "binary", "title": "Image"}, "index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["description", "title", "image", "index"], "title": "Body_aboutus-create_about_us"}, "Body_aboutus-edit_about_us": {"properties": {"description": {"type": "string", "title": "Description"}, "image": {"type": "string", "format": "binary", "title": "Image"}, "title": {"type": "string", "title": "Title"}, "index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["description", "title", "index"], "title": "Body_aboutus-edit_about_us"}, "Body_blogs-create_blog": {"properties": {"description": {"type": "string", "title": "Description"}, "title": {"type": "string", "title": "Title"}, "image1": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image1"}, "image2": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image2"}, "image3": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image3"}}, "type": "object", "required": ["description", "title"], "title": "Body_blogs-create_blog"}, "Body_blogs-delete_blog_images": {"properties": {"image": {"type": "string", "title": "Image"}}, "type": "object", "required": ["image"], "title": "Body_blogs-delete_blog_images"}, "Body_blogs-edit_blog": {"properties": {"description": {"type": "string", "title": "Description"}, "image1": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image1"}, "image2": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image2"}, "image3": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Image3"}, "title": {"type": "string", "title": "Title"}}, "type": "object", "required": ["description", "title"], "title": "Body_blogs-edit_blog"}, "Body_clients-client_login_access_token": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "password"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_clients-client_login_access_token"}, "Body_introduction-create_introduction": {"properties": {"description": {"type": "string", "title": "Description"}, "title": {"type": "string", "title": "Title"}, "image": {"type": "string", "format": "binary", "title": "Image"}, "index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["description", "title", "image", "index"], "title": "Body_introduction-create_introduction"}, "Body_introduction-edit_introduction": {"properties": {"description": {"type": "string", "title": "Description"}, "image": {"type": "string", "format": "binary", "title": "Image"}, "title": {"type": "string", "title": "Title"}, "index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["description", "title", "index"], "title": "Body_introduction-edit_introduction"}, "Body_login-login_access_token": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "password"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_login-login_access_token"}, "Body_ma_course-create_macourse": {"properties": {"image": {"type": "string", "format": "binary", "title": "Image"}, "index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["image", "index"], "title": "Body_ma_course-create_macourse"}, "Body_ma_course-edit_macourse": {"properties": {"index": {"type": "integer", "title": "Index"}}, "type": "object", "required": ["index"], "title": "Body_ma_course-edit_macourse"}, "Body_questionnaires-import_survey_questions": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_questionnaires-import_survey_questions"}, "ClientCreate": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Phone Number"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "ClientCreate"}, "ClientPublic": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Phone Number"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["email", "id"], "title": "ClientPublic"}, "ClientRegister": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Phone Number"}}, "type": "object", "required": ["email", "password"], "title": "ClientRegister"}, "ClientUpdate": {"properties": {"email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "password": {"anyOf": [{"type": "string", "maxLength": 40, "minLength": 8}, {"type": "null"}], "title": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Phone Number"}}, "type": "object", "title": "ClientUpdate"}, "ClientUpdateMe": {"properties": {"full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Phone Number"}}, "type": "object", "title": "ClientUpdateMe"}, "ClientsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ClientPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "ClientsPublic"}, "CoreCompetencyScore": {"properties": {"name": {"type": "string", "title": "Name"}, "score": {"type": "number", "title": "Score"}}, "type": "object", "required": ["name", "score"], "title": "CoreCompetencyScore"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "IntelligenceCategoryScore": {"properties": {"name": {"type": "string", "title": "Name"}, "total_score": {"type": "number", "title": "Total Score"}, "competencies": {"items": {"$ref": "#/components/schemas/CoreCompetencyScore"}, "type": "array", "title": "Competencies"}}, "type": "object", "required": ["name", "total_score", "competencies"], "title": "IntelligenceCategoryScore"}, "Introduction": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Description"}, "index": {"type": "integer", "title": "Index"}, "image": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image"}, "status": {"type": "boolean", "title": "Status", "default": true}, "id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}}, "type": "object", "required": ["title", "index"], "title": "Introduction"}, "IntroductionCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Description"}, "index": {"type": "integer", "title": "Index"}, "image": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Image"}, "status": {"type": "boolean", "title": "Status", "default": true}}, "type": "object", "required": ["title", "index"], "title": "IntroductionCreate"}, "IntroductionsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/Introduction"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "IntroductionsPublic"}, "ItemCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["title"], "title": "ItemCreate"}, "ItemPublic": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "owner_id": {"type": "string", "format": "uuid", "title": "Owner Id"}}, "type": "object", "required": ["title", "id", "owner_id"], "title": "ItemPublic"}, "ItemUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "ItemUpdate"}, "ItemsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ItemPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "ItemsPublic"}, "MaCourseImage": {"properties": {"image": {"type": "string", "title": "Image"}, "index": {"type": "integer", "title": "Index"}, "status": {"type": "boolean", "title": "Status", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["image", "index"], "title": "MaCourseImage"}, "MaCourseImagesPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/MaCourseImage"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "MaCourseImagesPublic"}, "Message": {"properties": {"message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["message"], "title": "Message"}, "NewPassword": {"properties": {"token": {"type": "string", "title": "Token"}, "new_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "New Password"}}, "type": "object", "required": ["token", "new_password"], "title": "NewPassword"}, "PrivateUserCreate": {"properties": {"email": {"type": "string", "title": "Email"}, "password": {"type": "string", "title": "Password"}, "full_name": {"type": "string", "title": "Full Name"}, "is_verified": {"type": "boolean", "title": "Is Verified", "default": false}}, "type": "object", "required": ["email", "password", "full_name"], "title": "PrivateUserCreate"}, "QuestionImportRow": {"properties": {"intelligence_category": {"type": "string", "title": "Intelligence Category"}, "core_competency": {"type": "string", "title": "Core Competency"}, "question": {"type": "string", "title": "Question"}}, "type": "object", "required": ["intelligence_category", "core_competency", "question"], "title": "QuestionImportRow"}, "QuestionPublic": {"properties": {"content": {"type": "string", "title": "Content"}, "index": {"type": "integer", "title": "Index"}, "competency_id": {"type": "integer", "title": "Competency Id"}, "survey_type_id": {"type": "integer", "title": "Survey Type Id"}, "question_id": {"type": "integer", "title": "Question Id"}}, "type": "object", "required": ["content", "index", "competency_id", "survey_type_id", "question_id"], "title": "QuestionPublic"}, "QuestionUpdate": {"properties": {"content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content"}, "index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Index"}}, "type": "object", "title": "QuestionUpdate"}, "QuestionWithDetails": {"properties": {"question_id": {"type": "integer", "title": "Question Id"}, "content": {"type": "string", "title": "Content"}, "index": {"type": "integer", "title": "Index"}, "intelligence_category": {"type": "string", "title": "Intelligence Category"}, "core_competency": {"type": "string", "title": "Core Competency"}}, "type": "object", "required": ["question_id", "content", "index", "intelligence_category", "core_competency"], "title": "QuestionWithDetails"}, "QuestionnaireAnswerRequest": {"properties": {"question_id": {"type": "integer", "title": "Question Id"}, "selected_answer": {"type": "string", "title": "Selected Answer"}}, "type": "object", "required": ["question_id", "selected_answer"], "title": "QuestionnaireAnswerRequest"}, "QuestionnaireCreate": {"properties": {"client_id": {"type": "string", "format": "uuid", "title": "Client Id"}}, "type": "object", "required": ["client_id"], "title": "QuestionnaireCreate"}, "QuestionnairePublic": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name"}, "birth_info": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Birth Info"}, "caregiver": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Caregiver"}, "feeding": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Feeding"}, "native_language": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Native Language"}, "grade_level": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Grade Level"}, "status": {"$ref": "#/components/schemas/QuestionnaireStatusEnum", "default": "available"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "client_id": {"type": "string", "format": "uuid", "title": "Client Id"}, "survey_type_id": {"type": "integer", "title": "Survey Type Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Finished At"}, "questions_and_ans": {"type": "object", "title": "Questions And Ans"}}, "type": "object", "required": ["name", "id", "client_id", "survey_type_id", "created_at", "finished_at", "questions_and_ans"], "title": "QuestionnairePublic"}, "QuestionnaireScoring": {"properties": {"questionnaire_id": {"type": "string", "title": "Questionnaire Id"}, "child_name": {"type": "string", "title": "Child Name"}, "survey_type_name": {"type": "string", "title": "Survey Type Name"}, "completed_at": {"type": "string", "title": "Completed At"}, "intelligence_categories": {"items": {"$ref": "#/components/schemas/IntelligenceCategoryScore"}, "type": "array", "title": "Intelligence Categories"}, "total_score": {"type": "number", "title": "Total Score"}}, "type": "object", "required": ["questionnaire_id", "child_name", "survey_type_name", "completed_at", "intelligence_categories", "total_score"], "title": "QuestionnaireScoring"}, "QuestionnaireStartRequest": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name"}, "birth_info": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Birth Info"}, "caregiver": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Caregiver"}, "feeding": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Feeding"}, "native_language": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Native Language"}, "grade_level": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Grade Level"}, "survey_type_id": {"type": "integer", "title": "Survey Type Id"}}, "type": "object", "required": ["name", "survey_type_id"], "title": "QuestionnaireStartRequest"}, "QuestionnaireStatusEnum": {"type": "string", "enum": ["available", "finished"], "title": "QuestionnaireStatusEnum"}, "QuestionnaireSubmitRequest": {"properties": {"answers": {"items": {"$ref": "#/components/schemas/QuestionnaireAnswerRequest"}, "type": "array", "title": "Answers"}}, "type": "object", "required": ["answers"], "title": "QuestionnaireSubmitRequest"}, "QuestionnaireWithDetails": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "client_id": {"type": "string", "format": "uuid", "title": "Client Id"}, "survey_type_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Survey Type Id"}, "survey_type_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Survey Type Name"}, "name": {"type": "string", "title": "Name"}, "birth_info": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Birth Info"}, "caregiver": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Caregiver"}, "feeding": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Feeding"}, "native_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Native Language"}, "grade_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade Level"}, "status": {"$ref": "#/components/schemas/QuestionnaireStatusEnum"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Finished At"}, "questions_and_ans": {"type": "object", "title": "Questions And Ans"}}, "type": "object", "required": ["id", "client_id", "survey_type_id", "survey_type_name", "name", "birth_info", "caregiver", "feeding", "native_language", "grade_level", "status", "created_at", "finished_at", "questions_and_ans"], "title": "QuestionnaireWithDetails"}, "QuestionnairesPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/QuestionnaireWithDetails"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "QuestionnairesPublic"}, "QuestionsImport": {"properties": {"questions": {"items": {"$ref": "#/components/schemas/QuestionImportRow"}, "type": "array", "title": "Questions"}}, "type": "object", "required": ["questions"], "title": "QuestionsImport"}, "Setting": {"properties": {"address": {"type": "string", "maxLength": 255, "title": "Address"}, "latitude": {"type": "number", "title": "Latitude"}, "longitude": {"type": "number", "title": "Longitude"}, "phone": {"type": "string", "maxLength": 255, "title": "Phone"}, "email": {"type": "string", "maxLength": 255, "title": "Email"}, "facebook": {"type": "string", "maxLength": 255, "title": "Facebook"}, "whatsapp": {"type": "string", "maxLength": 255, "title": "Whatsapp"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["address", "latitude", "longitude", "phone", "email", "facebook", "whatsapp"], "title": "Setting"}, "SettingBase": {"properties": {"address": {"type": "string", "maxLength": 255, "title": "Address"}, "latitude": {"type": "number", "title": "Latitude"}, "longitude": {"type": "number", "title": "Longitude"}, "phone": {"type": "string", "maxLength": 255, "title": "Phone"}, "email": {"type": "string", "maxLength": 255, "title": "Email"}, "facebook": {"type": "string", "maxLength": 255, "title": "Facebook"}, "whatsapp": {"type": "string", "maxLength": 255, "title": "Whatsapp"}}, "type": "object", "required": ["address", "latitude", "longitude", "phone", "email", "facebook", "whatsapp"], "title": "SettingBase"}, "SurveyQuestionsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/QuestionWithDetails"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "SurveyQuestionsPublic"}, "SurveyTypePublic": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name"}, "survey_type_id": {"type": "integer", "title": "Survey Type Id"}}, "type": "object", "required": ["name", "survey_type_id"], "title": "SurveyTypePublic"}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}}, "type": "object", "required": ["access_token"], "title": "Token"}, "UpdatePassword": {"properties": {"current_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Current Password"}, "new_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "New Password"}}, "type": "object", "required": ["current_password", "new_password"], "title": "UpdatePassword"}, "UserCreate": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "UserCreate"}, "UserPublic": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["email", "id"], "title": "UserPublic"}, "UserRegister": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}}, "type": "object", "required": ["email", "password"], "title": "UserRegister"}, "UserUpdate": {"properties": {"email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "password": {"anyOf": [{"type": "string", "maxLength": 40, "minLength": 8}, {"type": "null"}], "title": "Password"}}, "type": "object", "title": "UserUpdate"}, "UserUpdateMe": {"properties": {"full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}}, "type": "object", "title": "UserUpdateMe"}, "UsersPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/UserPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "UsersPublic"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/login/access-token"}}}}}}