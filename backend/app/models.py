import uuid
from datetime import datetime, timezone
import sqlalchemy

from enum import Enum
from pydantic import EmailStr
from sqlmodel import Field, Relationship, SQLModel, SQLModel, Column, Text


class AgeEnum(str, Enum):
    age_3_6 = "3-6"
    age_7_10 = "7-10"
    age_11_15 = "11-15"


class GenderEnum(str, Enum):
    m = "m"
    f = "f"


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)


# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title: str = Field(max_length=255)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


class ClientBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    full_name: str | None = Field(default=None, max_length=255)
    phone_number: str | None = Field(default=None, max_length=255)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


# Properties to receive via API on creation
class ClientCreate(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    full_name: str | None = Field(default=None, max_length=255)
    phone_number: str | None = Field(default=None, max_length=255)
    password: str = Field(min_length=8, max_length=40)


class ClientRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)
    phone_number: str | None = Field(default=None, max_length=255)


# Properties to receive via API on update, all are optional
class ClientUpdate(SQLModel):
    email: EmailStr | None = Field(default=None, max_length=255)
    is_active: bool | None = Field(default=None)
    password: str | None = Field(default=None, min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)
    phone_number: str | None = Field(default=None, max_length=255)


class ClientUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)
    phone_number: str | None = Field(default=None, max_length=255)


# Database model, database table inferred from class name
class Client(ClientBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    hashed_password: str


# Properties to return via API, id is always required
class ClientPublic(ClientBase):
    id: uuid.UUID


class ClientsPublic(SQLModel):
    data: list[ClientPublic]
    count: int


# main page introduction
class IntroductionBase(SQLModel):
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1024)
    index: int
    image: str | None = Field(default=None, max_length=255)
    status: bool = True


class IntroductionCreate(IntroductionBase):
    pass


class IntroductionUpdate(IntroductionBase):
    pass


class IntroductionCreateForm(SQLModel):
    """Model for documentation purposes only"""

    description: str
    title: str
    index: int
    image: str  # This represents the file upload


class Introduction(IntroductionBase, table=True):
    id: int | None = Field(default=None, primary_key=True)


class IntroductionsPublic(SQLModel):
    data: list[Introduction]
    count: int


# Ma course image


class MaCourseImageBase(SQLModel):
    image: str
    index: int
    status: bool = True


class MaCourseImageCreate(MaCourseImageBase):
    pass


class MaCourseImageUpdate(MaCourseImageBase):
    index: int


class MaCourseImage(MaCourseImageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)


class MaCourseImagesPublic(SQLModel):
    data: list[MaCourseImage]
    count: int


# Blogs


class BlogBase(SQLModel):
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=11024)
    image1: str | None = Field(default=None, max_length=255)
    image2: str | None = Field(default=None, max_length=255)
    image3: str | None = Field(default=None, max_length=255)
    status: bool = True


class Blog(BlogBase, table=True):
    id: int | None = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class BlogCreate(BlogBase):
    pass


class BlogUpdate(BlogBase):
    pass


class BlogsPublic(SQLModel):
    data: list[Blog]
    count: int


# about us


class AboutUsBase(SQLModel):
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1024)
    index: int
    image: str | None = Field(default=None, max_length=255)
    status: bool = True


class AboutUsCreate(AboutUsBase):
    pass


class AboutUsUpdate(AboutUsBase):
    pass


class AboutUs(AboutUsBase, table=True):
    id: int | None = Field(default=None, primary_key=True)


class AboutUsPublic(SQLModel):
    data: list[Introduction]
    count: int


#webSettings

class SettingBase(SQLModel):
    address: str = Field(max_length=255)
    latitude: float
    longitude: float
    phone: str = Field(max_length=255)
    email: str = Field(max_length=255)
    facebook: str = Field(max_length=255)
    whatsapp: str = Field(max_length=255)


class SettingCreate(SettingBase):
    pass


class Setting(SettingBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)


# Questionnaire Management System Models

class SurveyTypeBase(SQLModel):
    name: str = Field(max_length=255)


class SurveyType(SurveyTypeBase, table=True):
    survey_type_id: int = Field(primary_key=True)

    # Relationships
    questions: list["Question"] = Relationship(back_populates="survey_type")


class SurveyTypePublic(SurveyTypeBase):
    survey_type_id: int


class IntelligenceCategoryBase(SQLModel):
    name: str = Field(max_length=255)


class IntelligenceCategory(IntelligenceCategoryBase, table=True):
    category_id: int = Field(primary_key=True)

    # Relationships
    core_competencies: list["CoreCompetency"] = Relationship(back_populates="intelligence_category")


class IntelligenceCategoryPublic(IntelligenceCategoryBase):
    category_id: int


class CoreCompetencyBase(SQLModel):
    name: str = Field(max_length=255)
    category_id: int = Field(foreign_key="intelligencecategory.category_id")


class CoreCompetency(CoreCompetencyBase, table=True):
    competency_id: int = Field(primary_key=True)

    # Relationships
    intelligence_category: IntelligenceCategory = Relationship(back_populates="core_competencies")
    questions: list["Question"] = Relationship(back_populates="core_competency")


class CoreCompetencyPublic(CoreCompetencyBase):
    competency_id: int


class QuestionBase(SQLModel):
    content: str = Field(sa_column=Column(Text))
    index: int
    competency_id: int = Field(foreign_key="corecompetency.competency_id")
    survey_type_id: int = Field(foreign_key="surveytype.survey_type_id")


class Question(QuestionBase, table=True):
    question_id: int = Field(primary_key=True)

    # Relationships
    core_competency: CoreCompetency = Relationship(back_populates="questions")
    survey_type: SurveyType = Relationship(back_populates="questions")


class QuestionPublic(QuestionBase):
    question_id: int


class QuestionUpdate(SQLModel):
    content: str | None = None
    index: int | None = None


# Import/Export Models
class QuestionImportRow(SQLModel):
    intelligence_category: str
    core_competency: str
    question: str


class QuestionsImport(SQLModel):
    questions: list[QuestionImportRow]


# Survey Management Response Models
class QuestionWithDetails(SQLModel):
    question_id: int
    content: str
    index: int
    intelligence_category: str
    core_competency: str


class SurveyQuestionsPublic(SQLModel):
    data: list[QuestionWithDetails]
    count: int


# Client Questionnaire System Models

class QuestionnaireStatusEnum(str, Enum):
    available = "available"
    finished = "finished"


class QuestionnaireBase(SQLModel):
    name: str = Field(max_length=255)
    birth_info: str | None = Field(default=None, max_length=255)
    caregiver: str | None = Field(default=None, max_length=255)
    feeding: str | None = Field(default=None, max_length=255)
    native_language: str | None = Field(default=None, max_length=255)
    grade_level: str | None = Field(default=None, max_length=255)
    status: QuestionnaireStatusEnum = Field(default=QuestionnaireStatusEnum.available)


class QuestionnaireCreate(SQLModel):
    client_id: uuid.UUID


class QuestionnaireUpdate(SQLModel):
    name: str | None = Field(default=None, max_length=255)
    birth_info: str | None = Field(default=None, max_length=255)
    caregiver: str | None = Field(default=None, max_length=255)
    feeding: str | None = Field(default=None, max_length=255)
    native_language: str | None = Field(default=None, max_length=255)
    status: QuestionnaireStatusEnum | None = Field(default=None)
    questions_and_ans: dict | None = Field(default=None)
    finished_at: datetime | None = Field(default=None)


class Questionnaire(QuestionnaireBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    client_id: uuid.UUID = Field(foreign_key="client.id", nullable=False)
    survey_type_id: int | None = Field(default=None, foreign_key="surveytype.survey_type_id", nullable=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    finished_at: datetime | None = Field(default=None)
    questions_and_ans: dict = Field(default_factory=dict, sa_column=Column(sqlalchemy.JSON))

    # Relationships
    client: Client = Relationship()
    survey_type: SurveyType | None = Relationship()


class QuestionnairePublic(QuestionnaireBase):
    id: uuid.UUID
    client_id: uuid.UUID
    survey_type_id: int
    created_at: datetime
    finished_at: datetime | None
    questions_and_ans: dict


class QuestionnaireWithDetails(SQLModel):
    id: uuid.UUID
    client_id: uuid.UUID
    survey_type_id: int | None
    survey_type_name: str | None
    name: str
    birth_info: str | None
    caregiver: str | None
    feeding: str | None
    native_language: str | None
    grade_level: str | None
    status: QuestionnaireStatusEnum
    created_at: datetime
    finished_at: datetime | None
    questions_and_ans: dict


class QuestionnairesPublic(SQLModel):
    data: list[QuestionnaireWithDetails]
    count: int


# Client-side questionnaire models
class QuestionnaireStartRequest(SQLModel):
    name: str = Field(max_length=255)
    birth_info: str | None = Field(default=None, max_length=255)
    caregiver: str | None = Field(default=None, max_length=255)
    feeding: str | None = Field(default=None, max_length=255)
    native_language: str | None = Field(default=None, max_length=255)
    grade_level: str | None = Field(default=None, max_length=255)
    survey_type_id: int


class QuestionnaireAnswerRequest(SQLModel):
    question_id: int
    selected_answer: str = Field(regex="^[A-F]$")  # Only A, B, C, D, E, F allowed


class QuestionnaireSubmitRequest(SQLModel):
    answers: list[QuestionnaireAnswerRequest]