"""add grade_level field to questionnaire

Revision ID: 1c92810451c4
Revises: 09f9696116af
Create Date: 2025-07-18 06:38:16.095229

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '1c92810451c4'
down_revision = '09f9696116af'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('questionnaire', sa.Column('grade_level', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('questionnaire', 'grade_level')
    # ### end Alembic commands ###
