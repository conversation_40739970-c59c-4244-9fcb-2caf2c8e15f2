from typing import Any
import csv
import io
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlmodel import Session, select, delete
from app.api.deps import get_current_active_superuser, get_db, CurrentClient, CurrentUser
from app.models import (
    SurveyType,
    SurveyTypePublic,
    IntelligenceCategory,
    CoreCompetency,
    Question,
    QuestionWithDetails,
    SurveyQuestionsPublic,
    QuestionsImport,
    QuestionImportRow,
    QuestionUpdate,
    QuestionPublic,
    Message,
    # Client Questionnaire models
    Questionnaire,
    QuestionnaireCreate,
    QuestionnaireUpdate,
    QuestionnairePublic,
    QuestionnaireWithDetails,
    QuestionnairesPublic,
    QuestionnaireStartRequest,
    QuestionnaireAnswerRequest,
    QuestionnaireSubmitRequest,
    QuestionnaireStatusEnum,
    Client
)

router = APIRouter()


@router.get("/survey-types", response_model=list[SurveyTypePublic])
def get_survey_types(
    *,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Get all survey types.
    """
    survey_types = db.exec(select(SurveyType)).all()
    return survey_types


@router.get("/survey/{survey_type_id}", response_model=SurveyQuestionsPublic)
def get_survey_questions(
    *,
    survey_type_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Get all questions for a specific survey type with details.
    """
    # Verify survey type exists
    survey_type = db.get(SurveyType, survey_type_id)
    if not survey_type:
        raise HTTPException(status_code=404, detail="Survey type not found")
    
    # Get questions with intelligence category and core competency details
    query = (
        select(
            Question.question_id,
            Question.content,
            Question.index,
            IntelligenceCategory.name.label("intelligence_category"),
            CoreCompetency.name.label("core_competency")
        )
        .join(CoreCompetency, Question.competency_id == CoreCompetency.competency_id)
        .join(IntelligenceCategory, CoreCompetency.category_id == IntelligenceCategory.category_id)
        .where(Question.survey_type_id == survey_type_id)
        .order_by(Question.index)
    )
    
    results = db.exec(query).all()
    
    questions = [
        QuestionWithDetails(
            question_id=row.question_id,
            content=row.content,
            index=row.index,
            intelligence_category=row.intelligence_category,
            core_competency=row.core_competency
        )
        for row in results
    ]
    
    return SurveyQuestionsPublic(data=questions, count=len(questions))


def validate_import_data(questions_data: list[QuestionImportRow]) -> dict[str, set[str]]:
    """
    Analyze import data and return mapping of intelligence categories to core competencies.
    Raises HTTPException if validation rules are violated.

    Rules:
    - Must have exactly 8 different Intelligence Categories
    - Each Intelligence Category must have exactly 4 different Core Competencies

    Returns:
        dict: {intelligence_category: {core_competency1, core_competency2, ...}}
    """
    # Build mapping of intelligence categories to their core competencies
    category_mapping: dict[str, set[str]] = {}

    for question_row in questions_data:
        if not question_row.question.strip():
            continue  # Skip empty questions

        intelligence_category = question_row.intelligence_category.strip()
        core_competency = question_row.core_competency.strip()

        if intelligence_category not in category_mapping:
            category_mapping[intelligence_category] = set()

        category_mapping[intelligence_category].add(core_competency)

    # Validate the data
    validate_intelligence_categories_count(category_mapping)
    validate_core_competencies_per_category(category_mapping)

    return category_mapping


def validate_intelligence_categories_count(category_mapping: dict[str, set[str]]) -> None:
    """
    Validates that there are exactly 8 different Intelligence Categories.
    Raises HTTPException if validation fails.
    """
    category_count = len(category_mapping)

    if category_count != 8:
        categories_list = sorted(category_mapping.keys())
        if category_count < 8:
            raise HTTPException(
                status_code=400,
                detail=f"Import must contain exactly 8 different Intelligence Categories. "
                       f"Found {category_count} categories: {categories_list}. "
                       f"Please add {8 - category_count} more categories."
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Import must contain exactly 8 different Intelligence Categories. "
                       f"Found {category_count} categories: {categories_list}. "
                       f"Please remove {category_count - 8} categories."
            )


def validate_core_competencies_per_category(category_mapping: dict[str, set[str]]) -> None:
    """
    Validates that each Intelligence Category has exactly 4 different Core Competencies.
    Raises HTTPException if validation fails.
    """
    invalid_categories = []

    for category, competencies in category_mapping.items():
        competency_count = len(competencies)
        if competency_count != 4:
            invalid_categories.append({
                'category': category,
                'competency_count': competency_count,
                'competencies': sorted(competencies),
                'needed': 4 - competency_count
            })

    if invalid_categories:
        error_details = []
        for item in invalid_categories:
            if item['competency_count'] < 4:
                error_details.append(
                    f"'{item['category']}' has {item['competency_count']} competencies "
                    f"(exactly 4 required): {item['competencies']}. "
                    f"Please add {item['needed']} more competencies."
                )
            else:
                error_details.append(
                    f"'{item['category']}' has {item['competency_count']} competencies "
                    f"(exactly 4 required): {item['competencies']}. "
                    f"Please remove {-item['needed']} competencies."
                )

        raise HTTPException(
            status_code=400,
            detail=f"Each Intelligence Category must have exactly 4 different Core Competencies. "
                   f"Violations found: {'; '.join(error_details)}"
        )


@router.post("/survey/{survey_type_id}/import-json", response_model=Message)
def import_survey_questions_json(
    *,
    survey_type_id: int,
    questions_data: QuestionsImport,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Import questions from JSON data. Deletes all existing questions for the survey type.
    Expected format: {"questions": [{"intelligence_category": "...", "core_competency": "...", "question": "..."}]}

    Validation Rules:
    - Must have exactly 8 different Intelligence Categories
    - Each Intelligence Category must have exactly 4 different Core Competencies

    Returns 400 error with detailed message if validation fails.
    """
    # Verify survey type exists
    survey_type = db.get(SurveyType, survey_type_id)
    if not survey_type:
        raise HTTPException(status_code=404, detail="Survey type not found")

    # Validate import data before any database operations
    validate_import_data(questions_data.questions)

    try:
        # Delete existing questions for this survey type
        existing_questions = db.exec(
            select(Question).where(Question.survey_type_id == survey_type_id)
        ).all()
        for question in existing_questions:
            db.delete(question)

        # Process each question
        for index, question_row in enumerate(questions_data.questions, start=1):
            if not question_row.question.strip():
                continue  # Skip empty questions

            # Find or create intelligence category
            intelligence_category = db.exec(
                select(IntelligenceCategory).where(
                    IntelligenceCategory.name == question_row.intelligence_category.strip()
                )
            ).first()

            if not intelligence_category:
                intelligence_category = IntelligenceCategory(
                    name=question_row.intelligence_category.strip()
                )
                db.add(intelligence_category)
                db.flush()  # Get the ID

            # Find or create core competency
            core_competency = db.exec(
                select(CoreCompetency).where(
                    CoreCompetency.name == question_row.core_competency.strip(),
                    CoreCompetency.category_id == intelligence_category.category_id
                )
            ).first()

            if not core_competency:
                core_competency = CoreCompetency(
                    name=question_row.core_competency.strip(),
                    category_id=intelligence_category.category_id
                )
                db.add(core_competency)
                db.flush()  # Get the ID

            # Create question
            question = Question(
                content=question_row.question.strip(),
                index=index,
                competency_id=core_competency.competency_id,
                survey_type_id=survey_type_id
            )
            db.add(question)

        db.commit()

        return Message(message=f"Successfully imported {len(questions_data.questions)} questions")

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error processing questions: {str(e)}")


@router.post("/survey/{survey_type_id}/import", response_model=Message)
async def import_survey_questions(
    *,
    survey_type_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Import questions from CSV file. Deletes all existing questions for the survey type.
    Expected CSV headers: Intelligence Category, Core Competency, Question

    Validation Rules:
    - Must have exactly 8 different Intelligence Categories
    - Each Intelligence Category must have exactly 4 different Core Competencies

    Returns 400 error with detailed message if validation fails.
    """
    # Verify survey type exists
    survey_type = db.get(SurveyType, survey_type_id)
    if not survey_type:
        raise HTTPException(status_code=404, detail="Survey type not found")
    
    # Validate file type
    if not file.filename or not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV")
    
    try:
        # Read CSV content
        content = await file.read()
        csv_content = content.decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_content))
        
        # Validate headers
        expected_headers = {'Intelligence Category', 'Core Competency', 'Question'}
        if not expected_headers.issubset(set(csv_reader.fieldnames or [])):
            raise HTTPException(
                status_code=400, 
                detail=f"CSV must contain headers: {', '.join(expected_headers)}"
            )
        
        # Parse CSV rows
        questions_data = []
        for index, row in enumerate(csv_reader, start=1):
            if not row['Question'].strip():
                continue  # Skip empty questions
                
            questions_data.append({
                'intelligence_category': row['Intelligence Category'].strip(),
                'core_competency': row['Core Competency'].strip(),
                'question': row['Question'].strip(),
                'index': index
            })
        
        if not questions_data:
            raise HTTPException(status_code=400, detail="No valid questions found in CSV")

        # Convert to QuestionImportRow format for validation
        question_rows = [
            QuestionImportRow(
                intelligence_category=q['intelligence_category'],
                core_competency=q['core_competency'],
                question=q['question']
            )
            for q in questions_data
        ]

        # Validate import data before any database operations
        validate_import_data(question_rows)

        # Delete existing questions for this survey type
        db.exec(delete(Question).where(Question.survey_type_id == survey_type_id))
        
        # Process each question
        for question_data in questions_data:
            # Get or create intelligence category
            intelligence_category = db.exec(
                select(IntelligenceCategory).where(
                    IntelligenceCategory.name == question_data['intelligence_category']
                )
            ).first()
            
            if not intelligence_category:
                intelligence_category = IntelligenceCategory(name=question_data['intelligence_category'])
                db.add(intelligence_category)
                db.flush()  # Get the ID
            
            # Get or create core competency
            core_competency = db.exec(
                select(CoreCompetency).where(
                    CoreCompetency.name == question_data['core_competency'],
                    CoreCompetency.category_id == intelligence_category.category_id
                )
            ).first()
            
            if not core_competency:
                core_competency = CoreCompetency(
                    name=question_data['core_competency'],
                    category_id=intelligence_category.category_id
                )
                db.add(core_competency)
                db.flush()  # Get the ID
            
            # Create question
            question = Question(
                content=question_data['question'],
                index=question_data['index'],
                competency_id=core_competency.competency_id,
                survey_type_id=survey_type_id
            )
            db.add(question)
        
        db.commit()
        
        return Message(message=f"Successfully imported {len(questions_data)} questions")
        
    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="Invalid file encoding. Please use UTF-8")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error processing CSV: {str(e)}")


@router.patch("/question/{question_id}", response_model=QuestionPublic)
def update_question(
    *,
    question_id: int,
    question_update: QuestionUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Update a question. Only content and index can be updated.
    """
    question = db.get(Question, question_id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    # Update only the provided fields
    if question_update.content is not None:
        question.content = question_update.content
    if question_update.index is not None:
        question.index = question_update.index
    
    db.add(question)
    db.commit()
    db.refresh(question)
    
    return question


# ============================================================================
# CLIENT QUESTIONNAIRE MANAGEMENT ENDPOINTS
# ============================================================================

# Admin endpoints for questionnaire management

@router.get("/clients/{client_id}/questionnaires", response_model=QuestionnairesPublic)
def get_client_questionnaires(
    *,
    client_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Get all questionnaires for a specific client (Admin only).
    """
    # Verify client exists
    client = db.get(Client, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")

    # Get questionnaires with survey type details
    query = (
        select(
            Questionnaire.id,
            Questionnaire.client_id,
            Questionnaire.survey_type_id,
            SurveyType.name.label("survey_type_name"),
            Questionnaire.name,
            Questionnaire.birth_info,
            Questionnaire.caregiver,
            Questionnaire.feeding,
            Questionnaire.native_language,
            Questionnaire.grade_level,
            Questionnaire.status,
            Questionnaire.created_at,
            Questionnaire.finished_at,
            Questionnaire.questions_and_ans
        )
        .outerjoin(SurveyType, Questionnaire.survey_type_id == SurveyType.survey_type_id)
        .where(Questionnaire.client_id == client_id)
        .order_by(Questionnaire.created_at.desc())
    )

    results = db.exec(query).all()

    questionnaires = [
        QuestionnaireWithDetails(
            id=row.id,
            client_id=row.client_id,
            survey_type_id=row.survey_type_id,
            survey_type_name=row.survey_type_name,
            name=row.name,
            birth_info=row.birth_info,
            caregiver=row.caregiver,
            feeding=row.feeding,
            native_language=row.native_language,
            grade_level=row.grade_level,
            status=row.status,
            created_at=row.created_at,
            finished_at=row.finished_at,
            questions_and_ans=row.questions_and_ans
        )
        for row in results
    ]

    return QuestionnairesPublic(data=questionnaires, count=len(questionnaires))


@router.post("/clients/{client_id}/questionnaires", response_model=QuestionnairePublic)
def create_client_questionnaire(
    *,
    client_id: str,
    questionnaire_data: QuestionnaireCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Create a new questionnaire for a client (Admin only).
    Only one 'available' questionnaire per client is allowed.
    """
    # Verify client exists
    client = db.get(Client, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")

    # Survey type will be selected by client when they start the questionnaire

    # Check if client already has an available questionnaire
    existing_available = db.exec(
        select(Questionnaire).where(
            Questionnaire.client_id == client_id,
            Questionnaire.status == QuestionnaireStatusEnum.available
        )
    ).first()

    if existing_available:
        raise HTTPException(
            status_code=400,
            detail="Client already has an available questionnaire. Only one available questionnaire per client is allowed."
        )

    # Create new questionnaire (client will select survey type and fill info when they start)
    questionnaire = Questionnaire(
        client_id=client_id,
        survey_type_id=None,  # Will be selected by client
        name="",  # Will be filled by client
        birth_info=None,
        caregiver=None,
        feeding=None,
        native_language=None,
        status=QuestionnaireStatusEnum.available
    )

    db.add(questionnaire)
    db.commit()
    db.refresh(questionnaire)

    return questionnaire


@router.put("/questionnaires/{questionnaire_id}/status", response_model=QuestionnairePublic)
def update_questionnaire_status(
    *,
    questionnaire_id: str,
    status: QuestionnaireStatusEnum,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Update questionnaire status (Admin only).
    """
    questionnaire = db.get(Questionnaire, questionnaire_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")

    # If changing to finished, set finished_at timestamp
    if status == QuestionnaireStatusEnum.finished and questionnaire.status != QuestionnaireStatusEnum.finished:
        from datetime import datetime, timezone
        questionnaire.finished_at = datetime.now(timezone.utc)

    questionnaire.status = status
    db.add(questionnaire)
    db.commit()
    db.refresh(questionnaire)

    return questionnaire


@router.get("/questionnaires/{questionnaire_id}", response_model=QuestionnaireWithDetails)
def get_questionnaire(
    *,
    questionnaire_id: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser,
) -> Any:
    """
    Get detailed information about a specific questionnaire (Admin only).
    """
    # Get questionnaire with survey type details
    query = (
        select(
            Questionnaire.id,
            Questionnaire.client_id,
            Questionnaire.survey_type_id,
            SurveyType.name.label("survey_type_name"),
            Questionnaire.name,
            Questionnaire.birth_info,
            Questionnaire.caregiver,
            Questionnaire.feeding,
            Questionnaire.native_language,
            Questionnaire.grade_level,
            Questionnaire.status,
            Questionnaire.created_at,
            Questionnaire.finished_at,
            Questionnaire.questions_and_ans
        )
        .outerjoin(SurveyType, Questionnaire.survey_type_id == SurveyType.survey_type_id)
        .where(Questionnaire.id == questionnaire_id)
    )

    result = db.exec(query).first()

    if not result:
        raise HTTPException(
            status_code=404,
            detail="Questionnaire not found."
        )

    return QuestionnaireWithDetails(
        id=result.id,
        client_id=result.client_id,
        survey_type_id=result.survey_type_id,
        survey_type_name=result.survey_type_name,
        name=result.name,
        birth_info=result.birth_info,
        caregiver=result.caregiver,
        feeding=result.feeding,
        native_language=result.native_language,
        grade_level=result.grade_level,
        status=result.status,
        created_at=result.created_at,
        finished_at=result.finished_at,
        questions_and_ans=result.questions_and_ans
    )


@router.delete("/questionnaires/{questionnaire_id}", response_model=Message)
def delete_questionnaire(
    *,
    questionnaire_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_superuser)
) -> Any:
    """
    Delete a questionnaire (Admin only).
    """
    questionnaire = db.get(Questionnaire, questionnaire_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")

    db.delete(questionnaire)
    db.commit()

    return Message(message="Questionnaire deleted successfully")


# ============================================================================
# CLIENT-SIDE QUESTIONNAIRE ENDPOINTS
# ============================================================================

@router.get("/me/questionnaire", response_model=QuestionnairePublic | None)
def get_my_available_questionnaire(
    *,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Get the available questionnaire for the current client.
    Returns None if no available questionnaire exists.
    """
    questionnaire = db.exec(
        select(Questionnaire).where(
            Questionnaire.client_id == current_client.id,
            Questionnaire.status == QuestionnaireStatusEnum.available
        )
    ).first()

    return questionnaire


@router.get("/me/questionnaires", response_model=QuestionnairesPublic)
def get_my_questionnaires(
    *,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Get all questionnaires for the current client (both available and finished).
    """
    # Get questionnaires with survey type details
    query = (
        select(
            Questionnaire.id,
            Questionnaire.client_id,
            Questionnaire.survey_type_id,
            SurveyType.name.label("survey_type_name"),
            Questionnaire.name,
            Questionnaire.birth_info,
            Questionnaire.caregiver,
            Questionnaire.feeding,
            Questionnaire.native_language,
            Questionnaire.grade_level,
            Questionnaire.status,
            Questionnaire.created_at,
            Questionnaire.finished_at,
            Questionnaire.questions_and_ans
        )
        .outerjoin(SurveyType, Questionnaire.survey_type_id == SurveyType.survey_type_id)
        .where(Questionnaire.client_id == current_client.id)
        .order_by(Questionnaire.created_at.desc())
    )

    results = db.exec(query).all()

    questionnaires = [
        QuestionnaireWithDetails(
            id=row.id,
            client_id=row.client_id,
            survey_type_id=row.survey_type_id,
            survey_type_name=row.survey_type_name,
            name=row.name,
            birth_info=row.birth_info,
            caregiver=row.caregiver,
            feeding=row.feeding,
            native_language=row.native_language,
            grade_level=row.grade_level,
            status=row.status,
            created_at=row.created_at,
            finished_at=row.finished_at,
            questions_and_ans=row.questions_and_ans
        )
        for row in results
    ]

    return QuestionnairesPublic(data=questionnaires, count=len(questionnaires))


@router.get("/me/questionnaires/{questionnaire_id}", response_model=QuestionnaireWithDetails)
def get_my_questionnaire_details(
    *,
    questionnaire_id: str,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Get detailed information about a specific questionnaire for the current client.
    """
    # Get questionnaire with survey type details
    query = (
        select(
            Questionnaire.id,
            Questionnaire.client_id,
            Questionnaire.survey_type_id,
            SurveyType.name.label("survey_type_name"),
            Questionnaire.name,
            Questionnaire.birth_info,
            Questionnaire.caregiver,
            Questionnaire.feeding,
            Questionnaire.native_language,
            Questionnaire.grade_level,
            Questionnaire.status,
            Questionnaire.created_at,
            Questionnaire.finished_at,
            Questionnaire.questions_and_ans
        )
        .outerjoin(SurveyType, Questionnaire.survey_type_id == SurveyType.survey_type_id)
        .where(
            Questionnaire.id == questionnaire_id,
            Questionnaire.client_id == current_client.id
        )
    )

    result = db.exec(query).first()

    if not result:
        raise HTTPException(
            status_code=404,
            detail="Questionnaire not found or does not belong to current client."
        )

    return QuestionnaireWithDetails(
        id=result.id,
        client_id=result.client_id,
        survey_type_id=result.survey_type_id,
        survey_type_name=result.survey_type_name,
        name=result.name,
        birth_info=result.birth_info,
        caregiver=result.caregiver,
        feeding=result.feeding,
        native_language=result.native_language,
        grade_level=result.grade_level,
        status=result.status,
        created_at=result.created_at,
        finished_at=result.finished_at,
        questions_and_ans=result.questions_and_ans
    )


@router.post("/me/questionnaire/start", response_model=QuestionnairePublic)
def start_my_questionnaire(
    *,
    questionnaire_data: QuestionnaireStartRequest,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Start filling out the questionnaire by saving basic info and survey type.
    Updates the existing available questionnaire for the client.
    """
    # Get the client's available questionnaire
    questionnaire = db.exec(
        select(Questionnaire).where(
            Questionnaire.client_id == current_client.id,
            Questionnaire.status == QuestionnaireStatusEnum.available
        )
    ).first()

    if not questionnaire:
        raise HTTPException(
            status_code=404,
            detail="No available questionnaire found. Please contact support."
        )

    # Verify survey type exists
    survey_type = db.get(SurveyType, questionnaire_data.survey_type_id)
    if not survey_type:
        raise HTTPException(status_code=404, detail="Survey type not found")

    # Update questionnaire with basic info
    questionnaire.name = questionnaire_data.name
    questionnaire.birth_info = questionnaire_data.birth_info
    questionnaire.caregiver = questionnaire_data.caregiver
    questionnaire.feeding = questionnaire_data.feeding
    questionnaire.native_language = questionnaire_data.native_language
    questionnaire.grade_level = questionnaire_data.grade_level
    questionnaire.survey_type_id = questionnaire_data.survey_type_id

    # Initialize questions_and_ans with basic info
    questionnaire.questions_and_ans = {
        "basic_info": {
            "name": questionnaire_data.name,
            "birth_info": questionnaire_data.birth_info,
            "caregiver": questionnaire_data.caregiver,
            "feeding": questionnaire_data.feeding,
            "native_language": questionnaire_data.native_language,
            "grade_level": questionnaire_data.grade_level
        },
        "survey_type_id": questionnaire_data.survey_type_id,
        "survey_type_name": survey_type.name,
        "answers": []
    }

    db.add(questionnaire)
    db.commit()
    db.refresh(questionnaire)

    return questionnaire


@router.put("/me/questionnaire/answers", response_model=QuestionnairePublic)
def submit_questionnaire_answers(
    *,
    answers_data: QuestionnaireSubmitRequest,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Submit answers for the questionnaire.
    """
    # Get the client's available questionnaire
    questionnaire = db.exec(
        select(Questionnaire).where(
            Questionnaire.client_id == current_client.id,
            Questionnaire.status == QuestionnaireStatusEnum.available
        )
    ).first()

    if not questionnaire:
        raise HTTPException(
            status_code=404,
            detail="No available questionnaire found."
        )

    # Validate that all question IDs exist for the survey type and get detailed info
    question_ids = [answer.question_id for answer in answers_data.answers]

    # Get questions with core competency and intelligence category details
    query = (
        select(
            Question.question_id,
            Question.content,
            Question.index,
            CoreCompetency.name.label("core_competency_name"),
            IntelligenceCategory.name.label("intelligence_category_name")
        )
        .join(CoreCompetency, Question.competency_id == CoreCompetency.competency_id)
        .join(IntelligenceCategory, CoreCompetency.category_id == IntelligenceCategory.category_id)
        .where(
            Question.question_id.in_(question_ids),
            Question.survey_type_id == questionnaire.survey_type_id
        )
    )

    existing_questions = db.exec(query).all()

    if len(existing_questions) != len(question_ids):
        raise HTTPException(
            status_code=400,
            detail="Some question IDs are invalid for this survey type"
        )

    # Create question lookup with detailed information
    question_lookup = {
        q.question_id: {
            "content": q.content,
            "core_competency_name": q.core_competency_name,
            "intelligence_category_name": q.intelligence_category_name
        }
        for q in existing_questions
    }

    # Update the answers in the questionnaire
    current_data = questionnaire.questions_and_ans or {}

    answers_list = [
        {
            "question_id": answer.question_id,
            "question_text": question_lookup[answer.question_id]["content"],
            "core_competency_name": question_lookup[answer.question_id]["core_competency_name"],
            "intelligence_category_name": question_lookup[answer.question_id]["intelligence_category_name"],
            "selected_answer": answer.selected_answer,
            "answer_text": f"Option {answer.selected_answer}"
        }
        for answer in answers_data.answers
    ]

    # Create a completely new dictionary to ensure SQLAlchemy detects the change
    # This is necessary because SQLAlchemy doesn't detect in-place modifications to JSON fields
    new_data = {
        **current_data,
        "answers": answers_list
    }

    questionnaire.questions_and_ans = new_data
    db.add(questionnaire)
    db.commit()
    db.refresh(questionnaire)

    return questionnaire


@router.post("/me/questionnaire/finish", response_model=QuestionnairePublic)
def finish_my_questionnaire(
    *,
    db: Session = Depends(get_db),
    current_client: CurrentClient
) -> Any:
    """
    Mark the questionnaire as finished.
    """
    # Get the client's available questionnaire
    questionnaire = db.exec(
        select(Questionnaire).where(
            Questionnaire.client_id == current_client.id,
            Questionnaire.status == QuestionnaireStatusEnum.available
        )
    ).first()

    if not questionnaire:
        raise HTTPException(
            status_code=404,
            detail="No available questionnaire found."
        )

    # Check if questionnaire has answers
    if not questionnaire.questions_and_ans or not questionnaire.questions_and_ans.get("answers"):
        raise HTTPException(
            status_code=400,
            detail="Cannot finish questionnaire without answers."
        )

    # Mark as finished
    from datetime import datetime, timezone
    questionnaire.status = QuestionnaireStatusEnum.finished
    questionnaire.finished_at = datetime.now(timezone.utc)

    db.add(questionnaire)
    db.commit()
    db.refresh(questionnaire)

    return questionnaire


# Public endpoint for getting survey types (no auth required for client-side)
@router.get("/public/survey-types", response_model=list[SurveyTypePublic])
def get_public_survey_types(
    *,
    db: Session = Depends(get_db)
) -> Any:
    """
    Get all survey types (public endpoint for client-side).
    """
    survey_types = db.exec(select(SurveyType)).all()
    return survey_types


@router.get("/public/survey/{survey_type_id}/questions", response_model=SurveyQuestionsPublic)
def get_public_survey_questions(
    *,
    survey_type_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    Get all questions for a specific survey type with details (public client-side endpoint).
    """
    # Verify survey type exists
    survey_type = db.get(SurveyType, survey_type_id)
    if not survey_type:
        raise HTTPException(status_code=404, detail="Survey type not found")
    
    # Get questions with intelligence category and core competency details
    query = (
        select(
            Question.question_id,
            Question.content,
            Question.index,
            IntelligenceCategory.name.label("intelligence_category"),
            CoreCompetency.name.label("core_competency")
        )
        .join(CoreCompetency, Question.competency_id == CoreCompetency.competency_id)
        .join(IntelligenceCategory, CoreCompetency.category_id == IntelligenceCategory.category_id)
        .where(Question.survey_type_id == survey_type_id)
        .order_by(Question.index)
    )
    
    results = db.exec(query).all()
    
    questions = [
        QuestionWithDetails(
            question_id=row.question_id,
            content=row.content,
            index=row.index,
            intelligence_category=row.intelligence_category,
            core_competency=row.core_competency
        )
        for row in results
    ]
    
    return SurveyQuestionsPublic(data=questions, count=len(questions))
